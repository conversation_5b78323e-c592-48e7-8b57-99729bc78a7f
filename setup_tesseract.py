#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tesseract OCR 配置脚本
自动检测和配置 Tesseract OCR
"""

import os
import sys
import subprocess
from pathlib import Path

def find_tesseract():
    """查找 Tesseract 安装位置"""
    print("正在搜索 Tesseract OCR...")
    
    # 常见的安装路径
    common_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
        r"C:\tesseract\tesseract.exe",
        r"D:\Program Files\Tesseract-OCR\tesseract.exe",
        r"D:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
    ]
    
    # 检查常见路径
    for path in common_paths:
        if os.path.exists(path):
            print(f"✓ 找到 Tesseract: {path}")
            return path
    
    # 搜索整个 C 盘（可能比较慢）
    print("在常见位置未找到，正在搜索整个系统...")
    try:
        # 使用 PowerShell 搜索
        cmd = 'Get-ChildItem -Path "C:\\" -Recurse -Name "tesseract.exe" -ErrorAction SilentlyContinue | Select-Object -First 1'
        result = subprocess.run(['powershell', '-Command', cmd], 
                              capture_output=True, text=True, timeout=30)
        if result.stdout.strip():
            path = f"C:\\{result.stdout.strip()}"
            if os.path.exists(path):
                print(f"✓ 找到 Tesseract: {path}")
                return path
    except Exception as e:
        print(f"搜索过程中出错: {str(e)}")
    
    return None

def test_tesseract(tesseract_path):
    """测试 Tesseract 是否工作正常"""
    print(f"测试 Tesseract: {tesseract_path}")
    
    try:
        import pytesseract
        
        # 设置路径
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # 获取版本信息
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract 版本: {version}")
        
        # 测试中文支持
        try:
            # 检查是否支持中文
            langs = pytesseract.get_languages()
            if 'chi_sim' in langs:
                print("✓ 支持简体中文识别")
            else:
                print("! 警告: 未检测到中文语言包，可能影响车牌识别效果")
                print("  建议下载中文语言包: https://github.com/tesseract-ocr/tessdata")
        except:
            print("! 无法检测语言包支持")
        
        return True
        
    except Exception as e:
        print(f"✗ Tesseract 测试失败: {str(e)}")
        return False

def update_config_file(tesseract_path):
    """更新配置文件"""
    config_content = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tesseract OCR 配置文件
自动生成，请勿手动修改
"""

import pytesseract

# 设置 Tesseract 路径
pytesseract.pytesseract.tesseract_cmd = r"{tesseract_path}"

def setup_tesseract():
    """设置 Tesseract OCR"""
    try:
        version = pytesseract.get_tesseract_version()
        print(f"Tesseract OCR 已配置，版本: {{version}}")
        return True
    except Exception as e:
        print(f"Tesseract OCR 配置失败: {{str(e)}}")
        return False

if __name__ == "__main__":
    setup_tesseract()
'''
    
    try:
        with open('tesseract_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✓ 配置文件已创建: tesseract_config.py")
        return True
    except Exception as e:
        print(f"✗ 创建配置文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("Tesseract OCR 配置向导")
    print("=" * 50)
    
    # 1. 查找 Tesseract
    tesseract_path = find_tesseract()
    
    if not tesseract_path:
        print("\n✗ 未找到 Tesseract OCR")
        print("\n请按以下步骤安装:")
        print("1. 访问: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. 下载最新版本的 Windows 安装包")
        print("3. 安装时选择 '添加到 PATH' 选项")
        print("4. 重新运行此脚本")
        return False
    
    # 2. 测试 Tesseract
    if not test_tesseract(tesseract_path):
        print(f"\n✗ Tesseract 测试失败")
        print("请检查安装是否正确")
        return False
    
    # 3. 更新配置文件
    if not update_config_file(tesseract_path):
        return False
    
    print("\n" + "=" * 50)
    print("✓ Tesseract OCR 配置完成!")
    print("\n现在可以运行车牌识别程序:")
    print("1. python simple_app.py  # 启动 Web 应用")
    print("2. python quick_test.py <图像路径>  # 快速测试")
    print("3. python diagnosis_tool.py <图像路径>  # 详细诊断")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户取消操作")
    except Exception as e:
        print(f"\n配置过程中出错: {str(e)}")
        input("\n按回车键退出...")
