#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车管理模块，处理车辆进出和停车记录
"""

from datetime import datetime
from sqlalchemy import desc
from database.db_manager import db_session
from database.models import Vehicle, ParkingRecord, Member
from management.fee_calculator import calculate_fee

# 添加异常处理和事务管理
def register_entry(plate_number):
    try:
        # 验证输入
        if not plate_number or len(plate_number) > 10:
            raise ValueError("无效的车牌号")
            
        # 使用事务
        with db_session.begin():
            # 检查是否已有未完成的停车记录
            existing_record = ParkingRecord.query.filter_by(
                plate_number=plate_number, 
                exit_time=None
            ).first()
            
            if existing_record:
                return existing_record
                
            # 检查车辆是否存在，不存在则创建
            vehicle = Vehicle.query.filter_by(plate_number=plate_number).first()
            if not vehicle:
                vehicle = Vehicle(plate_number=plate_number)
                db_session.add(vehicle)
                db_session.flush()
            
            # 创建停车记录
            record = ParkingRecord(
                plate_number=plate_number,
                entry_time=datetime.now(),
                vehicle_id=vehicle.id
            )
            
            db_session.add(record)
            db_session.commit()
            
            print(f"车辆 {plate_number} 入场登记成功，时间: {record.entry_time}")
            return record
    except Exception as e:
        db_session.rollback()
        print(f"登记车辆入场失败: {str(e)}")
        raise

def register_exit(plate_number):
    """
    登记车辆出场
    
    Args:
        plate_number: 车牌号
        
    Returns:
        更新的停车记录，如果没有找到入场记录则返回None
    """
    # 查找未完成的停车记录
    record = ParkingRecord.query.filter_by(
        plate_number=plate_number, 
        exit_time=None
    ).first()
    
    if not record:
        print(f"未找到车辆 {plate_number} 的入场记录")
        return None
    
    # 更新出场时间
    record.exit_time = datetime.now()
    
    # 计算停车时长（小时）
    duration = (record.exit_time - record.entry_time).total_seconds() / 3600
    record.duration = round(duration, 2)
    
    # 计算停车费用
    record.fee = calculate_fee(plate_number, record.duration)
    
    db_session.commit()
    
    print(f"车辆 {plate_number} 出场登记成功，时间: {record.exit_time}，停车时长: {record.duration}小时，费用: {record.fee}元")
    return record

def get_parking_records(plate_number=None, start_date=None, end_date=None, limit=None):
    """
    获取停车记录
    
    Args:
        plate_number: 车牌号（可选）
        start_date: 开始日期（可选）
        end_date: 结束日期（可选）
        limit: 限制返回记录数量（可选）
        
    Returns:
        停车记录列表
    """
    query = ParkingRecord.query
    
    # 按车牌号筛选
    if plate_number:
        query = query.filter_by(plate_number=plate_number)
    
    # 按日期范围筛选
    if start_date:
        query = query.filter(ParkingRecord.entry_time >= start_date)
    if end_date:
        query = query.filter(ParkingRecord.entry_time <= end_date)
    
    # 按入场时间降序排序
    query = query.order_by(desc(ParkingRecord.entry_time))
    
    # 限制返回数量
    if limit:
        query = query.limit(limit)
    
    return query.all()

def get_current_parking_vehicles():
    """
    获取当前停车场内的车辆
    
    Returns:
        当前停车场内的车辆记录列表
    """
    return ParkingRecord.query.filter_by(exit_time=None).all()

def get_vehicle_history(plate_number, limit=10):
    """
    获取指定车辆的停车历史
    
    Args:
        plate_number: 车牌号
        limit: 限制返回记录数量
        
    Returns:
        车辆的停车记录列表
    """
    return ParkingRecord.query.filter_by(plate_number=plate_number)\
                             .order_by(desc(ParkingRecord.entry_time))\
                             .limit(limit)\
                             .all()

def get_parking_statistics():
    """
    获取停车场统计信息
    
    Returns:
        停车场统计信息字典
    """
    # 总车位数（假设为100）
    total_spaces = 100
    
    # 当前停车数量
    current_count = ParkingRecord.query.filter_by(exit_time=None).count()
    
    # 今日停车数量
    today = datetime.now().date()
    today_start = datetime.combine(today, datetime.min.time())
    today_count = ParkingRecord.query.filter(ParkingRecord.entry_time >= today_start).count()
    
    # 今日收入
    today_income = db_session.query(db_session.func.sum(ParkingRecord.fee))\
                            .filter(ParkingRecord.exit_time >= today_start)\
                            .scalar() or 0
    
    return {
        'total_spaces': total_spaces,
        'current_count': current_count,
        'available_spaces': total_spaces - current_count,
        'occupancy_rate': round(current_count / total_spaces * 100, 2),
        'today_count': today_count,
        'today_income': round(today_income, 2)
    }

def mark_payment(record_id, payment_method):
    """
    标记停车费用已支付
    
    Args:
        record_id: 停车记录ID
        payment_method: 支付方式
        
    Returns:
        更新后的停车记录
    """
    record = ParkingRecord.query.get(record_id)
    if not record:
        print(f"未找到ID为 {record_id} 的停车记录")
        return None
    
    record.paid = True
    record.payment_method = payment_method
    record.payment_time = datetime.now()
    
    db_session.commit()
    
    print(f"停车记录 {record_id} 已标记为已支付，支付方式: {payment_method}")
    return record
