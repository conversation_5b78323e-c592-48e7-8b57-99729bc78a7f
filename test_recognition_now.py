#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
立即测试车牌识别功能
"""

import os
import sys

def test_tesseract():
    """测试 Tesseract 配置"""
    print("=== 测试 Tesseract OCR ===")
    try:
        import pytesseract
        
        # 设置路径
        pytesseract.pytesseract.tesseract_cmd = r'E:\Tesseract OCR\tesseract.exe'
        
        # 获取版本
        version = pytesseract.get_tesseract_version()
        print(f"✓ Tesseract 版本: {version}")
        
        # 测试语言支持
        try:
            langs = pytesseract.get_languages()
            print(f"✓ 支持的语言: {', '.join(langs[:10])}...")
            if 'chi_sim' in langs:
                print("✓ 支持简体中文")
            else:
                print("! 警告: 未检测到中文支持")
        except:
            print("! 无法检测语言支持")
        
        return True
    except Exception as e:
        print(f"✗ Tesseract 测试失败: {str(e)}")
        return False

def test_dependencies():
    """测试依赖项"""
    print("\n=== 测试依赖项 ===")
    
    try:
        import cv2
        print(f"✓ OpenCV: {cv2.__version__}")
    except ImportError:
        print("✗ OpenCV 未安装")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy: {np.__version__}")
    except ImportError:
        print("✗ NumPy 未安装")
        return False
    
    try:
        from ultralytics import YOLO
        print("✓ YOLOv8 已安装")
    except ImportError:
        print("! YOLOv8 未安装，车辆检测可能不可用")
    
    return True

def test_plate_recognition_simple():
    """简单测试车牌识别模块"""
    print("\n=== 测试车牌识别模块 ===")
    
    try:
        # 导入模块
        from recognition.plate_recognizer import recognize_plate_from_image
        print("✓ 车牌识别模块导入成功")
        
        # 检查是否有测试图像
        test_images = []
        
        # 查找可能的测试图像
        possible_dirs = ['uploads', 'test_images', '.']
        for dir_name in possible_dirs:
            if os.path.exists(dir_name):
                for file in os.listdir(dir_name):
                    if file.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                        test_images.append(os.path.join(dir_name, file))
        
        if test_images:
            print(f"找到 {len(test_images)} 个图像文件")
            
            # 测试第一个图像
            test_image = test_images[0]
            print(f"测试图像: {test_image}")
            
            try:
                # 跳过车辆检测直接识别
                result = recognize_plate_from_image(test_image, skip_vehicle_detection=True)
                if result:
                    print(f"✓ 识别成功: {result}")
                else:
                    print("✗ 未识别到车牌")
            except Exception as e:
                print(f"✗ 识别过程出错: {str(e)}")
        else:
            print("! 未找到测试图像")
            print("请将车牌图像放在 uploads 目录中进行测试")
        
        return True
        
    except Exception as e:
        print(f"✗ 车牌识别模块测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("车牌识别系统测试")
    print("=" * 50)
    
    # 1. 测试 Tesseract
    if not test_tesseract():
        print("\n请先正确配置 Tesseract OCR")
        return
    
    # 2. 测试依赖项
    if not test_dependencies():
        print("\n请安装缺失的依赖项")
        return
    
    # 3. 测试车牌识别
    test_plate_recognition_simple()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("\n现在可以:")
    print("1. 运行 'python simple_app.py' 启动 Web 应用")
    print("2. 在浏览器中访问 http://localhost:5000")
    print("3. 上传车牌图像进行识别")

if __name__ == "__main__":
    main()
