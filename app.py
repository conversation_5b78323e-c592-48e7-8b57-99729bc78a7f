#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场管理系统主应用 - MySQL版本
"""

import os
import sys
import logging
import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, send_file
from flask_login import Login<PERSON>anager, login_user, logout_user, login_required, current_user

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 导入数据库相关模块
from database.db_manager import init_db, db_session, create_database_if_not_exists
from database.models import User, Vehicle, ParkingRecord, Member, ParkingFee, MembershipType, FinancialRecord
from recognition.plate_recognizer import recognize_plate_from_image
from recognition.video_processor import process_video_stream
from management.parking_manager import register_entry, register_exit, get_parking_records
from management.member_manager import get_all_members, add_member, update_member, delete_member
from management.fee_calculator import calculate_fee
from management.report_generator import generate_financial_report
# 导入二维码相关模块

# 创建Flask应用
app = Flask(__name__)
app.config.from_pyfile('config.py')

# 确保数据库和表结构存在
try:
    logger.info("初始化数据库连接...")
    # 确保数据库存在
    create_database_if_not_exists()
    # 初始化数据库表结构
    init_db()
    logger.info("数据库初始化成功")
except Exception as e:
    logger.error(f"数据库初始化失败: {str(e)}")
    sys.exit(1)

# 注册二维码上传路由
from qrcode_routes import qrcode_bp
app.register_blueprint(qrcode_bp, url_prefix='/qrcode')

# 初始化登录管理
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# 首页
@app.route('/')
def index():
    return render_template('index.html')

# 登录页面
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'danger')

    return render_template('login.html')

# 登出
@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('您已成功登出！', 'success')
    return redirect(url_for('index'))

# 管理员仪表板
@app.route('/dashboard')
@login_required
def dashboard():
    # 获取最近的停车记录
    recent_records = get_parking_records(limit=10)
    # 获取当前停车场状态
    parking_status = {
        'total_spaces': 100,  # 假设总车位数为100
        'occupied_spaces': ParkingRecord.query.filter_by(exit_time=None).count(),
        'available_spaces': 100 - ParkingRecord.query.filter_by(exit_time=None).count()
    }
    # 获取当前收费标准
    fee = ParkingFee.query.first()
    if not fee:
        fee = ParkingFee(hourly_rate=10.0, daily_max=100.0, free_minutes=15)  # 默认值
        db_session.add(fee)
        db_session.commit()

    # 获取会员数量
    member_count = Member.query.count()

    return render_template('dashboard.html', records=recent_records, status=parking_status,
                          fee=fee, member_count=member_count)

# 车牌识别 - 上传图片
@app.route('/recognize/upload', methods=['GET', 'POST'])
@login_required
def recognize_upload():
    if request.method == 'POST':
        if 'plate_image' not in request.files:
            flash('没有选择文件', 'danger')
            return redirect(request.url)

        file = request.files['plate_image']
        if file.filename == '':
            flash('没有选择文件', 'danger')
            return redirect(request.url)

        if file:
            # 保存上传的图片
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)

            # 识别车牌
            plate_number = recognize_plate_from_image(filename)

            if plate_number:
                # 记录车辆进出
                record = register_entry(plate_number)
                flash(f'识别成功！车牌号: {plate_number}', 'success')
                return render_template('recognition_result.html', plate_number=plate_number, record=record)
            else:
                # 检查Tesseract是否已安装
                import shutil
                tesseract_installed = shutil.which('tesseract') is not None

                if not tesseract_installed:
                    flash('无法识别车牌：Tesseract OCR未安装。<a href="/tesseract_guide">点击查看安装指南</a>', 'danger')
                else:
                    flash('无法识别车牌，请上传更清晰的图片或手动输入车牌号', 'warning')

    return render_template('upload_image.html')

# Tesseract OCR 安装指南
@app.route('/tesseract_guide')
def tesseract_guide():
    return render_template('tesseract_install.html')

# 车牌识别 - 实时视频
@app.route('/recognize/video')
@login_required
def recognize_video():
    return render_template('video_recognition.html')

# 车辆记录管理
@app.route('/records')
# 在数据库查询处添加参数化查询
@login_required
def view_records():
    plate_number = request.args.get('plate_number', '')
    # 使用SQLAlchemy的参数化查询
    query = ParkingRecord.query
    if plate_number:
        query = query.filter(ParkingRecord.plate_number.like(f'%{plate_number}%'))
    page = request.args.get('page', 1, type=int)
    per_page = 20

    # 获取总记录数（应用筛选条件后）
    total = query.count()

    # 手动实现分页
    query = query.order_by(ParkingRecord.entry_time.desc())
    items = query.offset((page - 1) * per_page).limit(per_page).all()

    # 创建分页对象
    class Pagination:
        def __init__(self, items, page, per_page, total):
            self.items = items
            self.page = page
            self.per_page = per_page
            self.total = total
            self.pages = (total + per_page - 1) // per_page

        @property
        def has_prev(self):
            return self.page > 1

        @property
        def has_next(self):
            return self.page < self.pages

        @property
        def prev_num(self):
            return self.page - 1 if self.has_prev else None

        @property
        def next_num(self):
            return self.page + 1 if self.has_next else None

        def iter_pages(self, left_edge=2, right_edge=2, left_current=2, right_current=2):
            last = 0
            for num in range(1, self.pages + 1):
                if num <= left_edge or \
                   (num > self.page - left_current - 1 and num < self.page + right_current) or \
                   num > self.pages - right_edge:
                    if last + 1 != num:
                        yield None
                    yield num
                    last = num

    records = Pagination(items, page, per_page, total)
    return render_template('records.html', records=records)

# 删除停车记录
@app.route('/records/delete/<int:record_id>', methods=['POST'])
@login_required
def delete_record(record_id):
    try:
        record = ParkingRecord.query.get_or_404(record_id)
        db_session.delete(record)
        db_session.commit()
        flash('停车记录删除成功！', 'success')
    except Exception as e:
        db_session.rollback()
        flash(f'删除停车记录失败: {str(e)}', 'danger')
    return redirect(url_for('view_records'))

# 导出停车记录为CSV
@app.route('/records/export')
@login_required
def export_records():
    import csv
    from io import StringIO
    import datetime

    # 获取查询参数
    plate_number = request.args.get('plate_number', '')

    # 打印调试信息
    print(f"导出记录，参数：plate_number={plate_number}")

    # 构建查询
    query = ParkingRecord.query
    if plate_number:
        query = query.filter(ParkingRecord.plate_number.like(f'%{plate_number}%'))

    # 获取所有记录
    records = query.order_by(ParkingRecord.entry_time.desc()).all()

    # 创建CSV文件
    output = StringIO()
    writer = csv.writer(output)

    # 写入表头
    writer.writerow(['ID', '车牌号', '入场时间', '出场时间', '停车时长(小时)', '费用(元)', '支付状态'])

    # 写入数据
    for record in records:
        writer.writerow([
            record.id,
            record.plate_number,
            record.entry_time.strftime('%Y-%m-%d %H:%M:%S') if record.entry_time else '',
            record.exit_time.strftime('%Y-%m-%d %H:%M:%S') if record.exit_time else '',
            record.duration if record.duration else '',
            record.fee if record.fee else '',
            '已支付' if record.paid else '未支付'
        ])

    # 设置响应头
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')
    response = app.response_class(
        response=output.getvalue(),
        mimetype='text/csv',
        headers={
            'Content-Disposition': f'attachment; filename=parking_records_{timestamp}.csv'
        }
    )

    return response

# 清空所有停车记录（仅用于导出后清空页面显示）
@app.route('/records/clear', methods=['POST'])
@login_required
def clear_records():
    try:
        # 获取当前在场的车辆（不删除）
        active_records = ParkingRecord.query.filter_by(exit_time=None).all()
        active_ids = [record.id for record in active_records]

        # 删除已经出场的车辆记录
        ParkingRecord.query.filter(ParkingRecord.exit_time != None).delete()
        db_session.commit()

        flash('停车记录已成功清空！当前在场车辆记录已保留。', 'success')
    except Exception as e:
        db_session.rollback()
        flash(f'清空停车记录失败: {str(e)}', 'danger')

    return redirect(url_for('view_records'))

# 会员管理
@app.route('/members')
@login_required
def view_members():
    members = get_all_members()
    return render_template('members.html', members=members)

# 添加会员
@app.route('/members/add', methods=['GET', 'POST'])
@login_required
def add_new_member():
    if request.method == 'POST':
        name = request.form['name']
        plate_number = request.form['plate_number']
        phone = request.form['phone']
        membership_type = request.form['membership_type']

        success = add_member(name, plate_number, phone, membership_type)
        if success:
            flash('会员添加成功！', 'success')
            return redirect(url_for('view_members'))
        else:
            flash('添加会员失败，请重试', 'danger')

    return render_template('add_member.html')

# 编辑会员
@app.route('/members/edit/<int:member_id>', methods=['GET', 'POST'])
@login_required
def edit_member(member_id):
    member = Member.query.get_or_404(member_id)

    if request.method == 'POST':
        member.name = request.form['name']
        member.plate_number = request.form['plate_number']
        member.phone = request.form['phone']
        member.membership_type = request.form['membership_type']

        success = update_member(member)
        if success:
            flash('会员信息更新成功！', 'success')
            return redirect(url_for('view_members'))
        else:
            flash('更新会员信息失败，请重试', 'danger')

    return render_template('edit_member.html', member=member)

# 删除会员
@app.route('/members/delete/<int:member_id>', methods=['POST'])
@login_required
def remove_member(member_id):
    success = delete_member(member_id)
    if success:
        flash('会员删除成功！', 'success')
    else:
        flash('删除会员失败，请重试', 'danger')
    return redirect(url_for('view_members'))

# 收费标准设置
@app.route('/fees', methods=['GET', 'POST'])
@login_required
def manage_fees():
    if request.method == 'POST':
        # 更新收费标准
        hourly_rate = float(request.form['hourly_rate'])
        daily_max = float(request.form['daily_max'])

        fee = ParkingFee.query.first()
        if not fee:
            fee = ParkingFee(hourly_rate=hourly_rate, daily_max=daily_max)
            db_session.add(fee)
        else:
            fee.hourly_rate = hourly_rate
            fee.daily_max = daily_max

        db_session.commit()
        flash('收费标准更新成功！', 'success')

    # 获取当前收费标准
    fee = ParkingFee.query.first()
    if not fee:
        fee = ParkingFee(hourly_rate=10.0, daily_max=100.0)  # 默认值
        db_session.add(fee)
        db_session.commit()

    return render_template('manage_fees.html', fee=fee)

# 财务报表
@app.route('/reports')
@login_required
def view_reports():
    report_type = request.args.get('type', 'daily')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    report_data = generate_financial_report(report_type, start_date, end_date)
    return render_template('reports.html', report_data=report_data, report_type=report_type)

# 导出报表为Excel
@app.route('/reports/export')
@login_required
def export_report():

    report_type = request.args.get('type', 'daily')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # 生成报表数据
    report_data = generate_financial_report(report_type, start_date, end_date)

    # 导出为Excel
    filename = f"parking_report_{report_type}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"

    # 创建一个内存文件对象
    from io import BytesIO
    output = BytesIO()

    # 使用pandas导出为Excel
    import pandas as pd

    # 创建DataFrame
    if report_data['daily_data']:
        df = pd.DataFrame(report_data['daily_data'])

        # 添加汇总行
        summary = pd.DataFrame([{
            'date': '总计',
            'income': report_data['total_income'],
            'count': report_data['total_vehicles']
        }])

        df = pd.concat([df, summary], ignore_index=True)

        # 导出到Excel
        df.to_excel(output, index=False, sheet_name='停车场报表')

        # 将指针移到文件开头
        output.seek(0)

        # 返回Excel文件
        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    else:
        # 如果没有数据，返回一个空的Excel文件
        df = pd.DataFrame(columns=['date', 'income', 'count'])
        df.to_excel(output, index=False, sheet_name='停车场报表')
        output.seek(0)

        return send_file(
            output,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

# API端点 - 实时视频识别
@app.route('/api/recognize_video', methods=['POST'])
@login_required
def api_recognize_video():
    if 'video_frame' in request.files:
        file = request.files['video_frame']
        filename = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_frame.jpg')
        file.save(filename)

        plate_number = recognize_plate_from_image(filename)
        if plate_number:
            # 检查是入场还是出场
            record = ParkingRecord.query.filter_by(plate_number=plate_number, exit_time=None).first()
            if record:
                # 已有入场记录，这是出场
                updated_record = register_exit(plate_number)
                return jsonify({
                    'success': True,
                    'plate_number': plate_number,
                    'action': 'exit',
                    'entry_time': updated_record.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'exit_time': updated_record.exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'duration': updated_record.duration,
                    'fee': updated_record.fee
                })
            else:
                # 没有入场记录，这是入场
                new_record = register_entry(plate_number)
                return jsonify({
                    'success': True,
                    'plate_number': plate_number,
                    'action': 'entry',
                    'entry_time': new_record.entry_time.strftime('%Y-%m-%d %H:%M:%S')
                })
        else:
            return jsonify({'success': False, 'error': '无法识别车牌'})

    return jsonify({'success': False, 'error': '没有接收到视频帧'})

# 手动输入车牌号
@app.route('/manual_plate_input', methods=['POST'])
@login_required
def manual_plate_input():
    plate_number = request.form.get('plate_number')

    if not plate_number:
        flash('请输入车牌号', 'danger')
        return redirect(url_for('recognize_upload'))

    # 记录车辆进出
    record = register_entry(plate_number)
    flash(f'车牌号: {plate_number}，已记录入场', 'success')
    return render_template('recognition_result.html', plate_number=plate_number, record=record)

# 车辆入场处理函数
@app.route('/vehicle_entry', methods=['POST'])
def vehicle_entry():
    # 获取图像并识别车牌
    image = request.files['image']  # 或从摄像头获取图像

    # 保存上传的图片
    filename = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_entry.jpg')
    image.save(filename)

    # 使用正确的函数名识别车牌
    plate_number = recognize_plate_from_image(filename)

    # 关键修改：只有在成功识别到车牌号时才录入车辆信息
    if plate_number is not None:
        # 录入车辆信息
        entry_time = datetime.datetime.now()  # 使用正确的datetime.datetime.now()
        # 数据库操作...

        return jsonify({
            'success': True,
            'plate_number': plate_number,
            'entry_time': entry_time
        })
    else:
        # 识别失败，返回错误信息
        return jsonify({
            'success': False,
            'message': '无法识别车牌号，请重试或手动输入'
        }), 400

    # 注意：这段代码永远不会执行，因为上面的if-else已经包含了所有可能的返回路径
    # 如果需要在GET请求时返回模板，应该在函数开头添加对请求方法的判断

# 清理资源
@app.teardown_appcontext
def shutdown_session(exception=None):
    db_session.remove()

if __name__ == '__main__':
    # 确保上传目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    app.run(debug=True)
