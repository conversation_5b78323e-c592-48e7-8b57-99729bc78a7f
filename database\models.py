#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库模型定义 - MySQL版本
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey, Text
from sqlalchemy.orm import relationship
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from database.db_manager import Base

class User(Base, UserMixin):
    """系统用户模型"""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='用户ID')
    username = Column(String(50), unique=True, nullable=False, comment='用户名')
    password_hash = Column(String(255), nullable=False, comment='密码哈希')
    email = Column(String(100), unique=True, nullable=False, comment='电子邮箱')
    is_admin = Column(Boolean, default=False, comment='是否管理员')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

class Vehicle(Base):
    """车辆信息模型"""
    __tablename__ = 'vehicles'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='车辆ID')
    plate_number = Column(String(20), unique=True, nullable=False, comment='车牌号')
    vehicle_type = Column(String(50), default='普通车辆', comment='车辆类型')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')

    # 关联
    member_id = Column(Integer, ForeignKey('members.id'), nullable=True, comment='会员ID')
    member = relationship("Member", back_populates="vehicles")
    parking_records = relationship("ParkingRecord", back_populates="vehicle")

    def __repr__(self):
        return f'<Vehicle {self.plate_number}>'

class ParkingRecord(Base):
    """停车记录模型"""
    __tablename__ = 'parking_records'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='记录ID')
    plate_number = Column(String(20), nullable=False, comment='车牌号')
    entry_time = Column(DateTime, default=datetime.now, comment='入场时间')
    exit_time = Column(DateTime, nullable=True, comment='出场时间')
    duration = Column(Float, nullable=True, comment='停车时长（小时）')
    fee = Column(Float, nullable=True, comment='停车费用')
    paid = Column(Boolean, default=False, comment='是否已支付')
    payment_method = Column(String(50), nullable=True, comment='支付方式')
    payment_time = Column(DateTime, nullable=True, comment='支付时间')
    notes = Column(Text, nullable=True, comment='备注信息')

    # 关联
    vehicle_id = Column(Integer, ForeignKey('vehicles.id'), comment='车辆ID')
    vehicle = relationship("Vehicle", back_populates="parking_records")

    def __repr__(self):
        return f'<ParkingRecord {self.plate_number} {self.entry_time}>'

class Member(Base):
    """会员信息模型"""
    __tablename__ = 'members'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='会员ID')
    name = Column(String(50), nullable=False, comment='会员姓名')
    phone = Column(String(20), nullable=False, comment='联系电话')
    membership_type = Column(String(50), default='普通会员', comment='会员类型：普通会员、VIP会员、企业会员等')
    discount_rate = Column(Float, default=1.0, comment='折扣率')
    balance = Column(Float, default=0.0, comment='账户余额')
    points = Column(Integer, default=0, comment='积分')
    start_date = Column(DateTime, default=datetime.now, comment='会员开始日期')
    end_date = Column(DateTime, nullable=True, comment='会员结束日期')
    is_active = Column(Boolean, default=True, comment='会员是否激活')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    notes = Column(Text, nullable=True, comment='备注信息')

    # 关联
    vehicles = relationship("Vehicle", back_populates="member")

    def __repr__(self):
        return f'<Member {self.name}>'

class ParkingFee(Base):
    """停车费用标准模型"""
    __tablename__ = 'parking_fees'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='费率ID')
    hourly_rate = Column(Float, nullable=False, default=10.0, comment='每小时费率')
    daily_max = Column(Float, nullable=False, default=100.0, comment='每日最高费用')
    free_minutes = Column(Integer, default=15, comment='免费停车时间（分钟）')
    description = Column(String(200), nullable=True, comment='费率描述')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    def __repr__(self):
        return f'<ParkingFee {self.hourly_rate}元/小时>'

class MembershipType(Base):
    """会员类型模型"""
    __tablename__ = 'membership_types'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='会员类型ID')
    name = Column(String(50), nullable=False, unique=True, comment='会员类型名称')
    discount_rate = Column(Float, nullable=False, default=1.0, comment='折扣率')
    monthly_fee = Column(Float, nullable=False, default=0.0, comment='月费')
    description = Column(String(200), nullable=True, comment='类型描述')
    created_at = Column(DateTime, default=datetime.now, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')

    def __repr__(self):
        return f'<MembershipType {self.name}>'

class FinancialRecord(Base):
    """财务记录模型"""
    __tablename__ = 'financial_records'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='财务记录ID')
    record_type = Column(String(20), nullable=False, comment='记录类型：收入、支出')
    amount = Column(Float, nullable=False, comment='金额')
    description = Column(String(200), nullable=True, comment='描述')
    record_time = Column(DateTime, default=datetime.now, comment='记录时间')
    payment_method = Column(String(50), nullable=True, comment='支付方式')
    operator = Column(String(50), nullable=True, comment='操作人')

    # 关联
    parking_record_id = Column(Integer, ForeignKey('parking_records.id'), nullable=True, comment='关联的停车记录ID')

    def __repr__(self):
        return f'<FinancialRecord {self.record_type} {self.amount}>'
