#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场管理系统配置文件
"""

import os

# 应用配置
SECRET_KEY = 'your-secret-key-here'
DEBUG = True

# 修改后
import os
from dotenv import load_dotenv

load_dotenv()

SECRET_KEY = os.getenv('SECRET_KEY', 'fallback-secret-key')
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

# 数据库配置
# MySQL连接配置
DB_USER = 'root'
DB_PASSWORD = '123456'
DB_HOST = 'localhost'
DB_PORT = '3306'
DB_NAME = 'car_park'
SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'
SQLALCHEMY_TRACK_MODIFICATIONS = False
SQLALCHEMY_ECHO = True  # 设置为True可以在控制台查看SQL语句执行情况

# 上传文件配置
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# 车牌识别配置
RECOGNITION_CONFIDENCE_THRESHOLD = 0.7

# 停车场配置
TOTAL_PARKING_SPACES = 100
