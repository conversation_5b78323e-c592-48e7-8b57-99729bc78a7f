Stack trace:
Frame         Function      Args
0007FFFFAF60  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFAF60, 0007FFFF9E60) msys-2.0.dll+0x1FEBA
0007FFFFAF60  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB238) msys-2.0.dll+0x67F9
0007FFFFAF60  000210046832 (000210285FF9, 0007FFFFAE18, 0007FFFFAF60, 000000000000) msys-2.0.dll+0x6832
0007FFFFAF60  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAF60  0002100690B4 (0007FFFFAF70, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFB240  00021006A49D (0007FFFFAF70, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCB0EE0000 ntdll.dll
7FFCAF170000 KERNEL32.DLL
7FFCAE740000 KERNELBASE.dll
7FFCAF870000 USER32.dll
7FFCAE030000 win32u.dll
7FFCAF5A0000 GDI32.dll
7FFCAE600000 gdi32full.dll
7FFCAE550000 msvcp_win.dll
7FFCAE1E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCAEE50000 advapi32.dll
7FFCB0700000 msvcrt.dll
7FFCAEF40000 sechost.dll
7FFCAECA0000 RPCRT4.dll
7FFCAD750000 CRYPTBASE.DLL
7FFCAE4B0000 bcryptPrimitives.dll
7FFCAFA40000 IMM32.DLL
