#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
报表生成模块
"""

import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from sqlalchemy import func, and_, desc
import os
import io
import base64

from database.db_manager import db_session
from database.models import ParkingRecord, Member, FinancialRecord

def generate_financial_report(report_type='daily', start_date=None, end_date=None):
    """
    生成财务报表

    Args:
        report_type: 报表类型（daily, weekly, monthly, yearly）
        start_date: 开始日期（可选）
        end_date: 结束日期（可选）

    Returns:
        报表数据字典
    """
    # 处理日期参数
    if start_date:
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
    else:
        # 默认日期范围
        if report_type == 'daily':
            start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        elif report_type == 'weekly':
            start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=7)
        elif report_type == 'monthly':
            start_date = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        elif report_type == 'yearly':
            start_date = datetime.now().replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

    if end_date:
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
    else:
        end_date = datetime.now()

    # 查询停车记录
    records = ParkingRecord.query.filter(
        and_(
            ParkingRecord.exit_time.isnot(None),
            ParkingRecord.exit_time >= start_date,
            ParkingRecord.exit_time <= end_date
        )
    ).all()

    # 计算总收入
    total_income = sum(record.fee for record in records if record.fee)

    # 计算平均停车时长
    total_duration = sum(record.duration for record in records if record.duration)
    avg_duration = total_duration / len(records) if records else 0

    # 按日期分组统计
    if report_type == 'daily':
        # 按小时统计
        date_format = '%Y-%m-%d %H:00'
        group_by = func.date_format(ParkingRecord.exit_time, '%Y-%m-%d %H:00')
    elif report_type == 'weekly':
        # 按日统计
        date_format = '%Y-%m-%d'
        group_by = func.date_format(ParkingRecord.exit_time, '%Y-%m-%d')
    elif report_type == 'monthly':
        # 按日统计
        date_format = '%Y-%m-%d'
        group_by = func.date_format(ParkingRecord.exit_time, '%Y-%m-%d')
    else:  # yearly
        # 按月统计
        date_format = '%Y-%m'
        group_by = func.date_format(ParkingRecord.exit_time, '%Y-%m')

    # 查询按日期分组的收入
    try:
        income_by_date = db_session.query(
            group_by.label('date'),
            func.sum(ParkingRecord.fee).label('income'),
            func.count(ParkingRecord.id).label('count')
        ).filter(
            and_(
                ParkingRecord.exit_time.isnot(None),
                ParkingRecord.exit_time >= start_date,
                ParkingRecord.exit_time <= end_date
            )
        ).group_by('date').order_by('date').all()
        print(f"成功查询到 {len(income_by_date)} 条收入数据")
    except Exception as e:
        print(f"查询收入数据时出错: {str(e)}")
        income_by_date = []

    # 转换为DataFrame
    df = pd.DataFrame([(item.date, item.income, item.count) for item in income_by_date],
                     columns=['date', 'income', 'count'])

    # 生成图表
    charts = {}
    if not df.empty:
        try:
            # 收入趋势图
            plt.figure(figsize=(10, 6))
            plt.plot(df['date'], df['income'], marker='o')
            plt.title('收入趋势')
            plt.xlabel('日期')
            plt.ylabel('收入（元）')
            plt.grid(True)
            plt.xticks(rotation=45)
            plt.tight_layout()

            # 保存图表为base64编码的图像
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            income_chart = base64.b64encode(buffer.getvalue()).decode('utf-8')
            charts['income_trend'] = income_chart
            plt.close()
            print("成功生成收入趋势图")
        except Exception as e:
            print(f"生成收入趋势图时出错: {str(e)}")

        try:
            # 车流量趋势图
            plt.figure(figsize=(10, 6))
            plt.plot(df['date'], df['count'], marker='o', color='green')
            plt.title('车流量趋势')
            plt.xlabel('日期')
            plt.ylabel('车辆数量')
            plt.grid(True)
            plt.xticks(rotation=45)
            plt.tight_layout()

            buffer = io.BytesIO()
            plt.savefig(buffer, format='png')
            buffer.seek(0)
            count_chart = base64.b64encode(buffer.getvalue()).decode('utf-8')
            charts['vehicle_trend'] = count_chart
            plt.close()
            print("成功生成车流量趋势图")
        except Exception as e:
            print(f"生成车流量趋势图时出错: {str(e)}")

    # 会员统计
    member_count = Member.query.count()  # 获取所有会员数量，不再筛选is_active

    # 最高收入日期
    max_income_date = None
    max_income = 0
    if not df.empty:
        max_income_idx = df['income'].idxmax()
        max_income_date = df.loc[max_income_idx, 'date']
        max_income = df.loc[max_income_idx, 'income']

    # 最高车流量日期
    max_count_date = None
    max_count = 0
    if not df.empty:
        max_count_idx = df['count'].idxmax()
        max_count_date = df.loc[max_count_idx, 'date']
        max_count = df.loc[max_count_idx, 'count']

    # 返回报表数据
    return {
        'report_type': report_type,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'total_income': round(total_income, 2),
        'total_vehicles': len(records),
        'avg_duration': round(avg_duration, 2),
        'member_count': member_count,
        'max_income_date': max_income_date,
        'max_income': round(max_income, 2) if max_income else 0,
        'max_count_date': max_count_date,
        'max_count': max_count,
        'daily_data': df.to_dict('records') if not df.empty else [],
        'charts': charts
    }

def export_records_to_excel(records, filename='parking_records.xlsx'):
    """
    将停车记录导出为Excel文件

    Args:
        records: 停车记录列表
        filename: 输出文件名

    Returns:
        文件路径
    """
    # 创建DataFrame
    data = []
    for record in records:
        entry_time = record.entry_time.strftime('%Y-%m-%d %H:%M:%S') if record.entry_time else ''
        exit_time = record.exit_time.strftime('%Y-%m-%d %H:%M:%S') if record.exit_time else ''

        data.append({
            '车牌号': record.plate_number,
            '入场时间': entry_time,
            '出场时间': exit_time,
            '停车时长(小时)': record.duration if record.duration else '',
            '费用(元)': record.fee if record.fee else '',
            '是否已支付': '是' if record.paid else '否',
            '支付方式': record.payment_method if record.payment_method else ''
        })

    df = pd.DataFrame(data)

    # 确保输出目录存在
    output_dir = 'exports'
    os.makedirs(output_dir, exist_ok=True)

    # 保存为Excel
    file_path = os.path.join(output_dir, filename)
    df.to_excel(file_path, index=False)

    return file_path

def generate_member_report():
    """
    生成会员报表

    Returns:
        会员报表数据字典
    """
    # 查询所有会员
    members = Member.query.all()

    # 会员类型分布
    membership_types = db_session.query(
        Member.membership_type,
        func.count(Member.id).label('count')
    ).group_by(Member.membership_type).all()

    # 活跃/非活跃会员 - 由于模型中有is_active字段，但我们不确定数据库中是否有这个字段，所以简化处理
    total_count = Member.query.count()
    active_count = total_count  # 假设所有会员都是活跃的
    inactive_count = 0

    # 会员车辆数量
    vehicle_counts = db_session.query(
        func.count(Member.id).label('member_count'),
        func.count(Member.vehicles).label('vehicle_count')
    ).first()

    # 生成图表
    charts = {}

    # 会员类型分布图
    if membership_types:
        plt.figure(figsize=(8, 8))
        labels = [item.membership_type for item in membership_types]
        sizes = [item.count for item in membership_types]
        plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90)
        plt.axis('equal')
        plt.title('会员类型分布')

        buffer = io.BytesIO()
        plt.savefig(buffer, format='png')
        buffer.seek(0)
        type_chart = base64.b64encode(buffer.getvalue()).decode('utf-8')
        charts['type_distribution'] = type_chart
        plt.close()

    # 活跃/非活跃会员图
    plt.figure(figsize=(8, 8))
    labels = ['活跃会员', '非活跃会员']
    sizes = [active_count, inactive_count]
    plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90, colors=['#66b3ff', '#ff9999'])
    plt.axis('equal')
    plt.title('会员活跃状态')

    buffer = io.BytesIO()
    plt.savefig(buffer, format='png')
    buffer.seek(0)
    active_chart = base64.b64encode(buffer.getvalue()).decode('utf-8')
    charts['active_status'] = active_chart
    plt.close()

    # 返回报表数据
    return {
        'total_members': len(members),
        'active_members': active_count,
        'inactive_members': inactive_count,
        'membership_types': [{'type': item.membership_type, 'count': item.count} for item in membership_types],
        'charts': charts
    }

if __name__ == "__main__":
    # 测试代码
    report = generate_financial_report('weekly')
    print(f"总收入: {report['total_income']}元")
    print(f"总车辆数: {report['total_vehicles']}")
    print(f"平均停车时长: {report['avg_duration']}小时")
