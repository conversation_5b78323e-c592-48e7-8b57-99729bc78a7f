<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传支付二维码 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('qrcode.upload_qrcode') }}">
                                <i class="bi bi-qr-code me-2"></i>
                                上传支付码
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">上传支付二维码</h1>
                </div>

                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">上传支付二维码</h6>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" action="{{ url_for('qrcode.upload_qrcode') }}">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        微信支付二维码
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="/static/img/your_wechat_qrcode.jpg" id="wechat-preview" class="img-fluid mb-3" style="max-height: 200px;" alt="微信支付二维码预览">
                                        <div class="mb-3">
                                            <label for="wechat_qrcode" class="form-label">选择微信支付二维码图片</label>
                                            <input class="form-control" type="file" id="wechat_qrcode" name="wechat_qrcode" accept="image/*" onchange="previewImage(this, 'wechat-preview')">
                                        </div>
                                        <div class="form-text">上传您的微信收款二维码图片</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        支付宝支付二维码
                                    </div>
                                    <div class="card-body text-center">
                                        <img src="/static/img/your_alipay_qrcode.jpg" id="alipay-preview" class="img-fluid mb-3" style="max-height: 200px;" alt="支付宝支付二维码预览">
                                        <div class="mb-3">
                                            <label for="alipay_qrcode" class="form-label">选择支付宝支付二维码图片</label>
                                            <input class="form-control" type="file" id="alipay_qrcode" name="alipay_qrcode" accept="image/*" onchange="previewImage(this, 'alipay-preview')">
                                        </div>
                                        <div class="form-text">上传您的支付宝收款二维码图片</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <h5>说明：</h5>
                            <ul>
                                <li>请上传清晰的收款二维码图片</li>
                                <li>支持的图片格式：JPG、JPEG、PNG、GIF</li>
                                <li>上传后的二维码将用于停车费支付</li>
                            </ul>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">保存二维码</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input, previewId) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById(previewId).src = e.target.result;
                }

                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>