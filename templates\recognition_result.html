<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>识别结果 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">识别结果</h1>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 bg-success text-white">
                                <h6 class="m-0 font-weight-bold">识别成功</h6>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <div class="display-4 mb-3">{{ plate_number }}</div>
                                    <div class="badge bg-primary fs-5 mb-3">
                                        {% if record.exit_time %}
                                            出场记录
                                        {% else %}
                                            入场记录
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="card border-left-primary h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">入场信息</h5>
                                                <p class="card-text">
                                                    <strong>时间：</strong> {{ record.entry_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-left-success h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">出场信息</h5>
                                                <p class="card-text">
                                                    {% if record.exit_time %}
                                                        <strong>时间：</strong> {{ record.exit_time.strftime('%Y-%m-%d %H:%M:%S') }}<br>
                                                        <strong>停车时长：</strong> {{ record.duration }} 小时<br>
                                                        <strong>费用：</strong> {{ record.fee }} 元
                                                    {% else %}
                                                        <span class="text-muted">车辆尚未出场</span>
                                                    {% endif %}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% if member %}
                                <div class="alert alert-info mb-4">
                                    <h5 class="alert-heading">会员信息</h5>
                                    <p>
                                        <strong>会员姓名：</strong> {{ member.name }}<br>
                                        <strong>会员类型：</strong> {{ member.membership_type }}<br>
                                        <strong>折扣率：</strong> {{ member.discount_rate * 100 }}%
                                    </p>
                                </div>
                                {% endif %}

                                <div class="text-center mt-4">
                                    <a href="{{ url_for('recognize_upload') }}" class="btn btn-primary me-2">
                                        <i class="bi bi-arrow-repeat me-2"></i>继续识别
                                    </a>
                                    <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                                        <i class="bi bi-house me-2"></i>返回仪表板
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
