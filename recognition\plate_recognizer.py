#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
车牌识别模块
"""

import os
import cv2
import numpy as np
import pytesseract
from PIL import Image
import os

# 设置Tesseract路径（如果需要）
pytesseract.pytesseract.tesseract_cmd = r'E:\Tesseract OCR\tesseract.exe'

# 改进车牌识别预处理
def preprocess_image(image_path):
    """
    预处理图像以便更好地识别车牌

    Args:
        image_path: 图像文件路径

    Returns:
        处理后的图像
    """
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        return None, None

    # 保存原始图像用于调试
    original_img = img.copy()

    # 转换为灰度图
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # 高斯模糊，减少噪声
    blur = cv2.GaussianBlur(gray, (5, 5), 0)

    # 自适应直方图均衡化
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    equalized = clahe.apply(blur)

    # 特别处理蓝色车牌 - 提取蓝色区域
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    # 蓝色范围 (中国蓝牌)
    lower_blue = np.array([100, 80, 80])
    upper_blue = np.array([130, 255, 255])
    blue_mask = cv2.inRange(hsv, lower_blue, upper_blue)

    # 形态学操作改进蓝色掩码
    kernel = np.ones((3, 3), np.uint8)
    blue_mask = cv2.morphologyEx(blue_mask, cv2.MORPH_CLOSE, kernel)
    blue_mask = cv2.morphologyEx(blue_mask, cv2.MORPH_OPEN, kernel)

    # 边缘检测
    edges = cv2.Canny(equalized, 100, 200)

    # 结合蓝色掩码和边缘
    combined_mask = cv2.bitwise_or(edges, blue_mask)

    # 膨胀和腐蚀操作，闭运算，消除噪点和小区域
    kernel = np.ones((3, 3), np.uint8)
    dilation = cv2.dilate(combined_mask, kernel, iterations=2)
    processed_mask = cv2.erode(dilation, kernel, iterations=1)

    # 添加自适应阈值处理
    adaptive_thresh = cv2.adaptiveThreshold(
        equalized, 255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV, 15, 2
    )

    # 添加形态学操作
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    morph = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)

    # 去除小区域噪声
    contours, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours:
        if cv2.contourArea(contour) < 100:  # 面积阈值
            cv2.drawContours(morph, [contour], -1, 0, -1)

    # 创建多种处理结果，提高识别成功率
    result1 = morph
    result2 = processed_mask
    result3 = adaptive_thresh

    # 保存处理后的图像用于调试
    debug_dir = os.path.join(os.path.dirname(image_path), 'debug')
    os.makedirs(debug_dir, exist_ok=True)
    cv2.imwrite(os.path.join(debug_dir, 'gray.jpg'), gray)
    cv2.imwrite(os.path.join(debug_dir, 'equalized.jpg'), equalized)
    cv2.imwrite(os.path.join(debug_dir, 'blue_mask.jpg'), blue_mask)
    cv2.imwrite(os.path.join(debug_dir, 'edges.jpg'), edges)
    cv2.imwrite(os.path.join(debug_dir, 'combined_mask.jpg'), combined_mask)
    cv2.imwrite(os.path.join(debug_dir, 'morph.jpg'), morph)
    cv2.imwrite(os.path.join(debug_dir, 'processed_mask.jpg'), processed_mask)

    return result1, original_img

def detect_plate_regions(processed_img, original_img):
    """
    检测图像中的车牌区域

    Args:
        processed_img: 预处理后的图像
        original_img: 原始图像

    Returns:
        可能的车牌区域列表
    """
    if processed_img is None or original_img is None:
        print("预处理图像或原始图像为空")
        return []

    # 查找轮廓
    contours, _ = cv2.findContours(processed_img, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    if len(contours) == 0:
        print("未检测到任何轮廓")
        return []

    # 筛选可能的车牌区域
    plate_regions = []
    height, width = original_img.shape[:2]
    min_area = width * height * 0.005  # 降低最小面积阈值，提升小车牌检测率
    max_area = width * height * 0.15   # 放宽最大面积阈值，适应近距离车牌

    # 记录筛选过程
    total_contours = len(contours)
    area_filtered = 0
    ratio_filtered = 0
    extent_filtered = 0
    edge_filtered = 0
    passed_filters = 0

    for contour in contours:
        # 计算轮廓面积
        area = cv2.contourArea(contour)

        # 面积筛选
        if area < min_area or area > max_area:
            area_filtered += 1
            continue

        # 获取矩形边界
        x, y, w, h = cv2.boundingRect(contour)

        # 计算宽高比，中国车牌宽高比约为3:1
        aspect_ratio = float(w) / h

        # 放宽宽高比范围，适应不同拍摄角度
        if not (1.8 < aspect_ratio < 5.0):
            ratio_filtered += 1
            continue

        # 计算矩形度
        rect_area = w * h
        extent = float(area) / rect_area
        if extent <= 0.6:  # 放宽矩形度阈值
            extent_filtered += 1
            continue

        # 检查区域是否靠近图像边缘
        margin = 5  # 放宽边缘距离
        if not (x > margin and y > margin and x + w < width - margin and y + h < height - margin):
            edge_filtered += 1
            continue

        # 通过所有筛选条件
        passed_filters += 1
        plate_regions.append((x, y, w, h))

    # 打印筛选统计信息
    print(f"轮廓总数: {total_contours}, 通过筛选: {passed_filters}")
    print(f"筛选详情 - 面积不符: {area_filtered}, 宽高比不符: {ratio_filtered}, 矩形度不符: {extent_filtered}, 靠近边缘: {edge_filtered}")

    # 如果没有找到符合条件的区域
    if not plate_regions:
        print("未找到符合车牌特征的区域")
        return []

    # 从原图中提取可能的车牌区域
    plate_images = []
    for x, y, w, h in plate_regions:
        # 扩大提取区域，包含车牌边框
        expand_x = int(w * 0.05)
        expand_y = int(h * 0.1)
        x1 = max(0, x - expand_x)
        y1 = max(0, y - expand_y)
        x2 = min(width, x + w + expand_x)
        y2 = min(height, y + h + expand_y)

        plate_img = original_img[y1:y2, x1:x2]
        if plate_img.size > 0:  # 确保提取的区域有效
            # 检查提取区域的颜色特征（车牌通常有特定的颜色分布）
            hsv = cv2.cvtColor(plate_img, cv2.COLOR_BGR2HSV)
            h_mean = np.mean(hsv[:,:,0])
            s_mean = np.mean(hsv[:,:,1])
            v_mean = np.mean(hsv[:,:,2])

            # 检查是否有合理的颜色分布（避免纯色区域）
            s_std = np.std(hsv[:,:,1])
            if s_std < 20:  # 饱和度标准差过小，可能不是车牌
                print(f"区域饱和度变化过小，可能不是车牌: {s_std:.2f}")
                continue

            plate_images.append(plate_img)
            print(f"找到可能的车牌区域: 位置({x},{y}), 尺寸({w}x{h}), 宽高比: {aspect_ratio:.2f}")

    return plate_images

def recognize_text_from_plate(plate_img):
    """
    从车牌图像中识别文本

    Args:
        plate_img: 车牌图像

    Returns:
        识别出的车牌号，如果识别失败返回None
    """
    if plate_img is None or plate_img.size == 0:
        print("车牌图像无效或为空")
        return None

    # 检查图像尺寸是否合理（车牌应该有一定的尺寸）
    height, width = plate_img.shape[:2]
    min_width, min_height = 60, 20  # 最小宽高要求
    if width < min_width or height < min_height:
        print(f"车牌图像尺寸过小: {width}x{height}，最小要求: {min_width}x{min_height}")
        return None

    # 转换为灰度图
    gray = cv2.cvtColor(plate_img, cv2.COLOR_BGR2GRAY)

    # 检查图像清晰度
    laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
    min_laplacian = 100  # 清晰度阈值
    if laplacian_var < min_laplacian:
        print(f"车牌图像不够清晰，清晰度值: {laplacian_var:.2f}，最小要求: {min_laplacian}")
        return None

    # 自适应直方图均衡化
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    equalized = clahe.apply(gray)

    # 尝试多种预处理方法，提高识别率
    results = []

    # 方法1: 自适应二值化处理
    binary1 = cv2.adaptiveThreshold(
        equalized, 255,
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY_INV, 11, 2
    )
    kernel = np.ones((2, 2), np.uint8)
    binary1 = cv2.morphologyEx(binary1, cv2.MORPH_CLOSE, kernel)
    binary1 = cv2.morphologyEx(binary1, cv2.MORPH_OPEN, kernel)

    # 方法2: Otsu二值化
    _, binary2 = cv2.threshold(equalized, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    binary2 = cv2.morphologyEx(binary2, cv2.MORPH_CLOSE, kernel)

    # 方法3: 边缘增强后二值化
    kernel_sharpen = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharp = cv2.filter2D(equalized, -1, kernel_sharpen)
    _, binary3 = cv2.threshold(sharp, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # 对每种预处理结果进行文本识别
    binaries = [binary1, binary2, binary3]

    # 使用Tesseract识别文本 - 改进中文识别
    config = '--psm 7 --oem 3 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼·'
    valid_chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼·'

    for idx, binary in enumerate(binaries):
        debug_dir = os.path.join(os.getcwd(), 'debug')
        os.makedirs(debug_dir, exist_ok=True)
        cv2.imwrite(os.path.join(debug_dir, f'plate_binary_{idx+1}.jpg'), binary)
        text = pytesseract.image_to_string(binary, lang='chi_sim+eng', config=config)

        # 清理识别结果
        text = text.strip().replace(' ', '').replace('\n', '')
        text = ''.join(c for c in text if c in valid_chars)
        print(f"OCR第{idx+1}种预处理结果: {text}")

        # 如果识别结果符合车牌规则，添加到结果列表
        if text and 6 <= len(text) <= 8:
            results.append(text)

    # 如果没有有效结果，返回None
    if not results:
        print("未能从车牌图像中识别出有效文本")
        return None

    # 返回出现频率最高的结果，提高可靠性
    from collections import Counter
    most_common = Counter(results).most_common(1)
    if most_common:
        return most_common[0][0]

    return results[0]  # 如果没有重复结果，返回第一个

def validate_plate_number(plate_number):
    """
    验证车牌号格式是否正确，支持特殊字符如点和中文间隔符

    Args:
        plate_number: 识别出的车牌号

    Returns:
        验证后的车牌号，如果格式不正确则返回None
    """
    if not plate_number:
        return None

    # 移除所有空白字符
    plate_number = ''.join(plate_number.split())

    # 处理特殊字符，如点和中文间隔符
    # 允许的特殊字符：点(.)、中点(·)、中文间隔号(·)
    special_chars = ['.', '·', '•', '・']

    # 标准化特殊字符为中文间隔号
    for char in special_chars:
        if char in plate_number:
            parts = plate_number.split(char)
            if len(parts) == 2:
                # 确保分隔符在第一个字符（省份）和第二个字符（字母）之间
                if len(parts[0]) == 1 and len(parts[1]) >= 1:
                    # 重新组合，使用标准中文间隔号
                    plate_number = parts[0] + '·' + parts[1]
                    break

    # 如果包含中文间隔号，特殊处理
    if '·' in plate_number:
        parts = plate_number.split('·')
        if len(parts) == 2:
            # 验证第一部分是否为省份简称
            provinces = '京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼'
            if parts[0] not in provinces:
                print(f"车牌首字符不是有效的省份简称: {parts[0]}")
                return None

            # 验证第二部分的第一个字符是否为字母
            if not parts[1] or not parts[1][0].isalpha():
                print(f"车牌分隔符后的第一个字符不是字母: {parts[1][0] if parts[1] else '无'}")
                return None

            # 验证第二部分的剩余字符是否为字母或数字
            remaining_chars = parts[1][1:]
            if not all(c.isalnum() for c in remaining_chars):
                print(f"车牌包含非法字符: {plate_number}")
                return None

            # 验证总长度（不包括间隔符）
            total_length = len(parts[0]) + len(parts[1])
            if total_length < 7 or total_length > 8:
                print(f"车牌长度不符合规范: {total_length}，应为7-8个字符")
                return None

            # 验证通过，返回原始格式（包含间隔符）
            print(f"车牌验证通过: {plate_number}")
            return plate_number

    # 常规验证（无特殊字符的情况）
    # 验证长度：中国车牌通常为7-8个字符
    if len(plate_number) < 7 or len(plate_number) > 8:
        print(f"车牌长度不符合规范: {len(plate_number)}，应为7-8个字符")
        return None

    # 验证第一个字符必须是省份简称
    provinces = '京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼'
    if plate_number[0] not in provinces:
        print(f"车牌首字符不是有效的省份简称: {plate_number[0]}")
        return None

    # 验证第二个字符必须是字母
    if not plate_number[1].isalpha():
        print(f"车牌第二个字符不是字母: {plate_number[1]}")
        return None

    # 验证剩余字符必须是字母或数字
    remaining_chars = plate_number[2:]
    if not all(c.isalnum() for c in remaining_chars):
        print(f"车牌包含非法字符: {plate_number}")
        return None

    # 验证特殊车牌格式
    if len(plate_number) == 8:
        # 新能源车牌规则：最后一位必须是数字
        if not plate_number[-1].isdigit():
            print(f"新能源车牌最后一位必须是数字: {plate_number}")
            return None

    # 验证常规车牌格式（7位）
    elif len(plate_number) == 7:
        # 验证后5位中至少有一位是数字
        if not any(c.isdigit() for c in plate_number[2:]):
            print(f"常规车牌后5位中至少应有一位数字: {plate_number}")
            return None

    # 检查是否有明显不合理的字符组合
    suspicious_patterns = ['000000', '111111', '123456', 'AAAAAA', 'ABCDEF']
    for pattern in suspicious_patterns:
        if pattern in plate_number[2:]:
            print(f"车牌包含可疑的字符模式: {plate_number}")
            return None

    print(f"车牌验证通过: {plate_number}")
    return plate_number

def recognize_plate_from_image(image_path, skip_vehicle_detection=False):
    """
    从图像中识别车牌号

    Args:
        image_path: 图像文件路径
        skip_vehicle_detection: 是否跳过车辆检测

    Returns:
        识别出的车牌号，如果未识别到则返回None
    """
    try:
        # 设置Tesseract路径
        import pytesseract
        try:
            # 尝试多个可能的Tesseract路径
            possible_paths = [
                r'E:\Tesseract OCR\tesseract.exe',  # 用户的实际安装路径
                r'C:\Program Files\Tesseract-OCR\tesseract.exe',
                r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
                r'C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe'.format(os.getenv('USERNAME', '')),
                'tesseract'  # 系统PATH中的tesseract
            ]

            tesseract_found = False
            for path in possible_paths:
                try:
                    if path == 'tesseract':
                        # 检查系统PATH中是否有tesseract
                        import shutil
                        if shutil.which('tesseract'):
                            pytesseract.pytesseract.tesseract_cmd = 'tesseract'
                            tesseract_found = True
                            break
                    else:
                        if os.path.exists(path):
                            pytesseract.pytesseract.tesseract_cmd = path
                            tesseract_found = True
                            break
                except:
                    continue

            if not tesseract_found:
                print("Tesseract OCR未找到，请安装Tesseract OCR")
                return None

            # 测试Tesseract是否可用
            version = pytesseract.get_tesseract_version()
            print(f"Tesseract版本: {version}")
        except Exception as te:
            print(f"Tesseract OCR未正确配置: {str(te)}")
            print("请安装Tesseract OCR并确保路径正确设置")
            return None

        # 车辆检测（可选）
        if not skip_vehicle_detection:
            try:
                from recognition.vehicle_detector import has_vehicle
                if not has_vehicle(image_path):
                    print("未检测到车辆，但继续尝试车牌识别...")
                    # 不直接返回None，而是继续尝试识别
            except ImportError:
                print("警告：车辆检测模块未找到，跳过车辆检测")
            except Exception as ve:
                print(f"车辆检测出错: {str(ve)}，跳过车辆检测")

        # 预处理图像
        processed_img, original_img = preprocess_image(image_path)
        if processed_img is None or original_img is None:
            print("图像预处理失败，无法继续识别")
            return None

        # 检测车牌区域
        plate_regions = detect_plate_regions(processed_img, original_img)

        # 如果没有检测到车牌区域，返回None
        if not plate_regions:
            print("未检测到车牌区域")
            return None

        # 对每个可能的车牌区域进行文本识别
        for plate_img in plate_regions:
            plate_number = recognize_text_from_plate(plate_img)

            # 验证车牌号格式
            valid_plate = validate_plate_number(plate_number)
            if valid_plate:
                return valid_plate

        # 如果所有区域都未能识别出有效车牌，返回None
        print("检测到车牌区域，但无法识别有效的车牌号")
        return None

    except Exception as e:
        import traceback
        print(f"车牌识别出错: {str(e)}")
        print(traceback.format_exc())
        return None

# 测试函数
def test_recognition(image_path):
    """
    测试车牌识别功能

    Args:
        image_path: 测试图像路径
    """
    print(f"\n开始测试图像: {image_path}")
    print("步骤1: 检测图像中是否存在车辆")
    try:
        from recognition.vehicle_detector import has_vehicle
        vehicle_detected = has_vehicle(image_path)
        if vehicle_detected:
            print("✓ 检测到车辆，继续进行车牌识别")
        else:
            print("✗ 未检测到车辆，停止车牌识别")
            return
    except ImportError:
        print("! 警告：车辆检测模块未找到，跳过车辆检测步骤")

    print("\n步骤2: 识别车牌")
    plate_number = recognize_plate_from_image(image_path)
    if plate_number:
        print(f"✓ 识别成功，车牌号: {plate_number}")
    else:
        print("✗ 未能识别车牌")
    print("测试完成\n")

if __name__ == "__main__":
    # 测试代码
    test_image = "test_plate.jpg"  # 替换为实际测试图像路径
    if os.path.exists(test_image):
        test_recognition(test_image)
    else:
        print(f"测试图像 {test_image} 不存在")

    # 可以添加更多测试图像
    # test_images = ["test_plate.jpg", "no_vehicle.jpg", "blurry_plate.jpg"]
    # for img in test_images:
    #     if os.path.exists(img):
    #         test_recognition(img)
    #     else:
    #         print(f"测试图像 {img} 不存在")
