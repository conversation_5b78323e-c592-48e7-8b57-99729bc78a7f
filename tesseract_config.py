#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tesseract OCR 配置文件
请根据您的实际安装路径修改 tesseract_cmd
"""

import pytesseract
import os

# 可能的 Tesseract 安装路径，请根据实际情况修改
POSSIBLE_PATHS = [
    r"E:\Tesseract OCR\tesseract.exe",  # 用户的实际安装路径
    r"C:\Program Files\Tesseract-OCR\tesseract.exe",
    r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
    r"C:\tesseract\tesseract.exe",
    r"D:\Program Files\Tesseract-OCR\tesseract.exe",
    "tesseract"  # 如果已添加到 PATH
]

def setup_tesseract():
    """自动设置 Tesseract 路径"""
    for path in POSSIBLE_PATHS:
        try:
            if path == "tesseract":
                # 检查是否在 PATH 中
                import shutil
                if shutil.which("tesseract"):
                    pytesseract.pytesseract.tesseract_cmd = "tesseract"
                    version = pytesseract.get_tesseract_version()
                    print(f"✓ Tesseract 配置成功 (PATH): {version}")
                    return True
            else:
                # 检查文件是否存在
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    version = pytesseract.get_tesseract_version()
                    print(f"✓ Tesseract 配置成功: {path}")
                    print(f"  版本: {version}")
                    return True
        except Exception as e:
            continue

    print("✗ 未找到 Tesseract OCR")
    print("请确保 Tesseract 已正确安装")
    print("下载地址: https://github.com/UB-Mannheim/tesseract/wiki")
    return False

# 自动配置
if __name__ == "__main__":
    setup_tesseract()
else:
    # 当作为模块导入时自动配置
    setup_tesseract()
