#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的数据存储模块，用于保存应用数据
"""

import os
import json
import datetime

# 数据文件路径
DATA_FILE = 'parking_data.json'

# 初始数据
default_data = {
    'parking_records': [],
    'members': [
        {
            'id': 1,
            'name': '张三',
            'plate_number': '京A12345',
            'phone': '13800138000',
            'membership_type': 'VIP会员',
            'is_active': True
        },
        {
            'id': 2,
            'name': '李四',
            'plate_number': '沪B54321',
            'phone': '13900139000',
            'membership_type': '普通会员',
            'is_active': True
        },
        {
            'id': 3,
            'name': '王五',
            'plate_number': '粤C98765',
            'phone': '13700137000',
            'membership_type': '企业会员',
            'is_active': False
        }
    ],
    'fee_settings': {
        'hourly_rate': 10.0,
        'daily_max': 100.0,
        'free_minutes': 15
    },
    'discount_settings': {
        'normal': {'discount': 90, 'fee': 50},
        'vip': {'discount': 80, 'fee': 100},
        'corporate': {'discount': 70, 'fee': 200}
    }
}

def datetime_serializer(obj):
    """处理datetime对象的JSON序列化"""
    if isinstance(obj, (datetime.datetime, datetime.date)):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")

def datetime_deserializer(dct):
    """处理datetime对象的JSON反序列化"""
    for key, value in dct.items():
        if isinstance(value, str) and 'T' in value:
            try:
                dct[key] = datetime.datetime.fromisoformat(value)
            except ValueError:
                pass
    return dct

def load_data():
    """加载数据"""
    if not os.path.exists(DATA_FILE):
        return default_data

    try:
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f, object_hook=datetime_deserializer)
        return data
    except Exception as e:
        print(f"加载数据出错: {str(e)}")
        return default_data

def save_data(data):
    """保存数据"""
    try:
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=datetime_serializer)
        return True
    except Exception as e:
        print(f"保存数据出错: {str(e)}")
        return False

# 获取会员信息
def get_members():
    data = load_data()
    return data['members']

# 更新会员信息
def update_member(member_id, name, plate_number, phone, membership_type):
    data = load_data()
    for member in data['members']:
        if member['id'] == member_id:
            member['name'] = name
            member['plate_number'] = plate_number
            member['phone'] = phone
            member['membership_type'] = membership_type
            save_data(data)
            return True
    return False

# 添加会员
def add_member(name, plate_number, phone, membership_type):
    data = load_data()
    # 生成新ID
    new_id = 1
    if data['members']:
        new_id = max(member['id'] for member in data['members']) + 1

    # 创建新会员
    new_member = {
        'id': new_id,
        'name': name,
        'plate_number': plate_number,
        'phone': phone,
        'membership_type': membership_type,
        'is_active': True
    }

    data['members'].append(new_member)
    save_data(data)
    return new_member

# 删除会员
def delete_member(member_id):
    data = load_data()
    data['members'] = [m for m in data['members'] if m['id'] != member_id]
    save_data(data)
    return True

# 获取停车记录
def get_parking_records():
    data = load_data()
    return data['parking_records']

# 添加停车记录
def add_parking_record(record):
    data = load_data()
    # 生成新ID
    new_id = 1
    if data['parking_records']:
        new_id = max(record['id'] for record in data['parking_records'] if 'id' in record) + 1

    record['id'] = new_id
    data['parking_records'].append(record)
    save_data(data)
    return record

# 更新停车记录
def update_parking_record(record_id, updates):
    data = load_data()
    for record in data['parking_records']:
        if record['id'] == record_id:
            # 确保记录中有exit_note字段
            if 'exit_note' not in record:
                record['exit_note'] = ''

            record.update(updates)
            save_data(data)
            return record
    return None

# 获取收费设置
def get_fee_settings():
    data = load_data()
    return data['fee_settings']

# 更新收费设置
def update_fee_settings(hourly_rate, daily_max, free_minutes):
    data = load_data()
    data['fee_settings'] = {
        'hourly_rate': float(hourly_rate),
        'daily_max': float(daily_max),
        'free_minutes': int(free_minutes)
    }
    save_data(data)
    return data['fee_settings']

# 获取折扣设置
def get_discount_settings():
    data = load_data()
    return data['discount_settings']

# 更新折扣设置
def update_discount_settings(normal_discount, normal_fee, vip_discount, vip_fee, corporate_discount, corporate_fee):
    data = load_data()
    data['discount_settings'] = {
        'normal': {'discount': float(normal_discount), 'fee': float(normal_fee)},
        'vip': {'discount': float(vip_discount), 'fee': float(vip_fee)},
        'corporate': {'discount': float(corporate_discount), 'fee': float(corporate_fee)}
    }
    save_data(data)
    return data['discount_settings']

# 标记停车记录为已支付
def mark_record_as_paid(record_id, payment_method, payment_amount):
    data = load_data()
    for record in data['parking_records']:
        if record['id'] == record_id:
            record['paid'] = True
            record['payment_method'] = payment_method
            record['payment_amount'] = float(payment_amount)
            record['payment_time'] = datetime.datetime.now()
            save_data(data)
            return record
    return None

# 删除单条停车记录
def delete_parking_record(record_id):
    data = load_data()
    original_length = len(data['parking_records'])
    data['parking_records'] = [r for r in data['parking_records'] if r['id'] != record_id]
    if len(data['parking_records']) < original_length:
        save_data(data)
        return True
    return False

# 清空所有停车记录
def clear_parking_records():
    data = load_data()
    data['parking_records'] = []
    save_data(data)
    return True

# 保存停车记录
def save_parking_records(records):
    data = load_data()
    data['parking_records'] = records
    save_data(data)
    return True
