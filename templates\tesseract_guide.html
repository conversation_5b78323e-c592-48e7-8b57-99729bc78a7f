<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tesseract OCR 安装指南 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Tesseract OCR 安装指南</h1>
                </div>

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">安装步骤</h6>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle-fill me-2"></i>
                                    车牌识别功能需要安装 Tesseract OCR 才能正常工作。请按照以下步骤安装。
                                </div>

                                <h5 class="mt-4">Windows 安装步骤</h5>
                                <ol class="mt-3">
                                    <li class="mb-3">
                                        <strong>下载 Tesseract OCR 安装程序：</strong>
                                        <ul>
                                            <li>访问 <a href="https://github.com/UB-Mannheim/tesseract/wiki" target="_blank">https://github.com/UB-Mannheim/tesseract/wiki</a></li>
                                            <li>下载最新版本的安装程序（例如：tesseract-ocr-w64-setup-v5.3.3.20231005.exe）</li>
                                        </ul>
                                    </li>
                                    <li class="mb-3">
                                        <strong>运行安装程序：</strong>
                                        <ul>
                                            <li>选择"Additional language data (download)"选项</li>
                                            <li>在语言选择中，确保选择了"Chinese (Simplified)"和"English"</li>
                                            <li>记住安装路径（默认为 <code>C:\Program Files\Tesseract-OCR</code>）</li>
                                        </ul>
                                    </li>
                                    <li class="mb-3">
                                        <strong>添加环境变量：</strong>
                                        <ul>
                                            <li>右键点击"此电脑"，选择"属性"</li>
                                            <li>点击"高级系统设置"</li>
                                            <li>点击"环境变量"</li>
                                            <li>在"系统变量"部分，找到"Path"变量并编辑</li>
                                            <li>添加 Tesseract 安装路径（例如：<code>C:\Program Files\Tesseract-OCR</code>）</li>
                                            <li>点击"确定"保存更改</li>
                                        </ul>
                                    </li>
                                    <li class="mb-3">
                                        <strong>验证安装：</strong>
                                        <ul>
                                            <li>重新打开命令提示符或 PowerShell</li>
                                            <li>运行 <code>tesseract --version</code> 命令</li>
                                            <li>如果显示版本信息，则安装成功</li>
                                        </ul>
                                    </li>
                                </ol>

                                <div class="alert alert-warning mt-4">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <strong>重要提示：</strong> 安装完成后，请重启应用程序以使更改生效。
                                </div>

                                <div class="text-center mt-4">
                                    <a href="{{ url_for('recognize_upload') }}" class="btn btn-primary">
                                        <i class="bi bi-arrow-left me-2"></i>返回上传识别页面
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
