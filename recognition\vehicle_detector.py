#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
车辆检测模块，用于检测图像中是否存在车辆
"""

import cv2
import numpy as np
from ultralytics import YOLO

class VehicleDetector:
    def __init__(self):
        try:
            # 加载YOLOv8模型
            self.model = YOLO('yolov8n.pt')
            print("成功加载YOLOv8模型")
        except Exception as e:
            print(f"加载YOLOv8模型失败: {str(e)}")
            print("尝试使用本地模型或使用备用方法...")
            # 如果无法下载模型，可以尝试使用OpenCV的DNN模块作为备用
            self.model = None

        # 定义车辆相关的类别ID (car, truck, bus, motorcycle)
        self.vehicle_classes = [2, 7, 5, 3]
        # 定义人类ID，用于过滤
        self.person_class = 0

    def detect_vehicle(self, image_path):
        """
        检测图像中是否存在车辆

        Args:
            image_path: 图像文件路径

        Returns:
            tuple: (是否存在车辆, 车辆区域坐标列表)
        """
        print(f"\n开始分析图像: {image_path}")
        print("------------------------------")
        try:
            # 读取图像
            img = cv2.imread(image_path)
            if img is None:
                print(f"无法读取图像: {image_path}")
                return False, []

            # 检查模型是否成功加载
            if self.model is None:
                print("模型未成功加载，无法进行检测")
                return False, []

            # 使用YOLOv8进行目标检测
            results = self.model(img)

            # 获取检测结果
            vehicle_boxes = []
            person_detected = False
            height, width = img.shape[:2]
            # 优化检测参数 - 提高参数严格性以避免人像误检
            min_area_ratio = 0.05  # 提高车辆最小面积占比，减少小物体误检
            max_area_ratio = 0.7   # 降低车辆最大面积占比，避免整个图像被识别为车辆
            min_aspect_ratio = 0.8  # 提高最小宽高比，车辆通常更宽
            max_aspect_ratio = 2.0  # 降低最大宽高比，避免过于细长的物体被识别为车辆

            for result in results:
                boxes = result.boxes
                for box in boxes:
                    # 获取类别ID和置信度
                    cls = int(box.cls[0])
                    conf = float(box.conf[0])

                    # 检测是否有人
                    if cls == self.person_class and conf > 0.5:
                        person_detected = True
                        print(f"检测到人像，置信度: {conf:.2f}")

                    # 降低车辆置信度阈值到0.5，提高检测率
                    if cls in self.vehicle_classes and conf > 0.5:
                        # 获取边界框坐标
                        x1, y1, x2, y2 = map(int, box.xyxy[0])

                        # 计算车辆区域特征
                        box_width = x2 - x1
                        box_height = y2 - y1
                        box_area = box_width * box_height
                        image_area = width * height
                        area_ratio = box_area / image_area
                        aspect_ratio = box_width / box_height

                        # 验证车辆特征
                        if (min_area_ratio <= area_ratio <= max_area_ratio and
                            min_aspect_ratio <= aspect_ratio <= max_aspect_ratio):
                            vehicle_boxes.append((x1, y1, x2, y2))

            # 添加额外验证：如果检测到人像，需要进行更严格的验证
            # 人像检测优先级高于车辆检测
            if person_detected:
                # 如果没有检测到车辆，直接判定为无车辆
                if len(vehicle_boxes) == 0:
                    print("检测到人像但未检测到车辆，判定为无车辆")
                    return False, []

                # 如果检测到车辆，进行额外验证：检查车辆区域与人像区域是否有明显区别
                # 获取人像区域
                person_boxes = []
                for result in results:
                    boxes = result.boxes
                    for box in boxes:
                        cls = int(box.cls[0])
                        conf = float(box.conf[0])
                        if cls == self.person_class and conf > 0.5:
                            x1, y1, x2, y2 = map(int, box.xyxy[0])
                            person_boxes.append((x1, y1, x2, y2))

                            # 人脸检测 - 如果检测到人脸，增加额外验证
                            try:
                                # 提取人像区域
                                person_roi = img[y1:y2, x1:x2]
                                if person_roi.size > 0:
                                    # 使用OpenCV内置的人脸检测器
                                    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                                    gray_roi = cv2.cvtColor(person_roi, cv2.COLOR_BGR2GRAY)
                                    faces = face_cascade.detectMultiScale(gray_roi, 1.1, 4)

                                    # 如果检测到人脸，记录但不阻止车辆检测
                                    if len(faces) > 0:
                                        print(f"检测到人脸，但仍继续检测车辆")
                            except Exception as e:
                                print(f"人脸检测出错: {str(e)}")
                                # 错误不影响主流程

                # 检查车辆和人像是否为同一区域（重叠度高）
                for p_box in person_boxes:
                    for v_box in vehicle_boxes.copy():  # 使用copy避免在迭代中修改列表
                        p_x1, p_y1, p_x2, p_y2 = p_box
                        v_x1, v_y1, v_x2, v_y2 = v_box

                        # 计算重叠区域
                        overlap_x1 = max(p_x1, v_x1)
                        overlap_y1 = max(p_y1, v_y1)
                        overlap_x2 = min(p_x2, v_x2)
                        overlap_y2 = min(p_y2, v_y2)

                        if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
                            overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
                            person_area = (p_x2 - p_x1) * (p_y2 - p_y1)
                            vehicle_area = (v_x2 - v_x1) * (v_y2 - v_y1)

                            # 如果重叠区域占人像区域的比例过高，认为是误检测
                            overlap_ratio = overlap_area / person_area
                            vehicle_overlap_ratio = overlap_area / vehicle_area

                            # 同时检查两个重叠比例，但使用更宽松的阈值
                            if overlap_ratio > 0.8 and vehicle_overlap_ratio > 0.8:  # 提高重叠阈值到80%
                                print(f"检测到车辆与人像重叠度过高({overlap_ratio:.2f})，判定为误检测")
                                vehicle_boxes.remove(v_box)

                # 重新判断是否有有效车辆
                if len(vehicle_boxes) == 0:
                    print("过滤重叠区域后无有效车辆，判定为无车辆")
                    return False, []

            # 添加车辆验证逻辑：至少要有一个车辆，且置信度足够高
            if len(vehicle_boxes) > 0:
                print(f"检测到{len(vehicle_boxes)}个车辆")
                return True, vehicle_boxes
            else:
                return False, []

        except Exception as e:
            print(f"车辆检测出错: {str(e)}")
            return False, []

# 创建全局检测器实例
vehicle_detector = VehicleDetector()

def has_vehicle(image_path):
    """
    检查图像中是否存在车辆的便捷函数

    Args:
        image_path: 图像文件路径

    Returns:
        布尔值，表示是否存在车辆
    """
    has_vehicle, _ = vehicle_detector.detect_vehicle(image_path)
    return has_vehicle


def debug_detection(image_path, save_visualization=True):
    """
    调试车辆检测功能，并可选择保存可视化结果

    Args:
        image_path: 图像文件路径
        save_visualization: 是否保存可视化结果

    Returns:
        检测结果和可视化图像路径
    """
    print(f"\n调试车辆检测: {image_path}")

    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        print(f"无法读取图像: {image_path}")
        return False, None

    # 创建检测器实例的副本进行调试
    detector = VehicleDetector()

    # 使用YOLOv8进行目标检测
    results = detector.model(img)

    # 创建可视化图像
    vis_img = img.copy()

    # 绘制所有检测到的对象
    person_count = 0
    vehicle_count = 0

    # 获取COCO数据集的类别名称
    class_names = detector.model.names

    for result in results:
        boxes = result.boxes
        for box in boxes:
            cls = int(box.cls[0])
            conf = float(box.conf[0])

            # 获取边界框坐标
            x1, y1, x2, y2 = map(int, box.xyxy[0])

            # 根据类别设置颜色
            if cls == detector.person_class:
                color = (0, 0, 255)  # 红色表示人
                person_count += 1
            elif cls in detector.vehicle_classes:
                color = (0, 255, 0)  # 绿色表示车辆
                vehicle_count += 1
            else:
                color = (255, 0, 0)  # 蓝色表示其他物体

            # 绘制边界框
            cv2.rectangle(vis_img, (x1, y1), (x2, y2), color, 2)

            # 添加类别和置信度标签
            label = f"{class_names[cls]}: {conf:.2f}"
            cv2.putText(vis_img, label, (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)

    # 添加统计信息
    info_text = f"人数: {person_count}, 车辆: {vehicle_count}"
    cv2.putText(vis_img, info_text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 165, 255), 2)

    # 保存可视化结果
    if save_visualization:
        vis_path = image_path.replace('.jpg', '_detection.jpg').replace('.png', '_detection.png')
        cv2.imwrite(vis_path, vis_img)
        print(f"可视化结果已保存至: {vis_path}")

    # 调用检测函数
    has_vehicle, boxes = vehicle_detector.detect_vehicle(image_path)

    # 返回检测结果
    return has_vehicle, vis_img


if __name__ == "__main__":
    # 测试代码
    import sys
    if len(sys.argv) > 1:
        test_image = sys.argv[1]
        try:
            result, vis_img = debug_detection(test_image)
            if vis_img is not None:
                print(f"\n检测结果: {'有车辆' if result else '无车辆'}")
                print(f"可视化结果已保存至: {test_image.replace('.jpg', '_detection.jpg').replace('.png', '_detection.png')}")
            else:
                print("\n检测失败，无法生成可视化结果")
        except Exception as e:
            print(f"\n运行检测时出错: {str(e)}")
    else:
        print("请提供测试图像路径作为参数")
        print("用法: python vehicle_detector.py <图像路径>")
        print("例如: python vehicle_detector.py test_image.jpg")
        print("\n提示: 如果模型下载失败，请手动下载yolov8n.pt模型文件并放置在当前目录")