#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建占位图片
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_placeholder_image():
    """创建占位图片"""
    # 创建目录
    os.makedirs('static/img', exist_ok=True)
    
    # 创建一个空白图片
    width, height = 300, 200
    img = Image.new('RGB', (width, height), color=(240, 240, 240))
    
    # 添加文字
    draw = ImageDraw.Draw(img)
    try:
        # 尝试加载字体，如果失败则使用默认字体
        font = ImageFont.truetype("arial.ttf", 20)
    except IOError:
        font = ImageFont.load_default()
    
    text = "请上传车牌图片"
    # 计算文本位置，使其居中
    text_width = draw.textlength(text, font=font)
    position = ((width - text_width) / 2, height / 2 - 10)
    
    # 绘制文字
    draw.text(position, text, font=font, fill=(100, 100, 100))
    
    # 保存图片
    filename = 'static/img/placeholder.png'
    img.save(filename)
    print(f"占位图片已保存到 {filename}")
    
    return filename

if __name__ == "__main__":
    create_placeholder_image()
