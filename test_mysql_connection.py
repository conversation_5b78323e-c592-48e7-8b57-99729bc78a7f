#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MySQL数据库连接测试脚本
"""

import os
import sys
import pymysql
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入配置
from config import DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME

def test_mysql_connection():
    """测试MySQL数据库连接"""
    print(f"测试连接到MySQL数据库: {DB_HOST}:{DB_PORT}/{DB_NAME}")
    
    try:
        # 尝试连接到MySQL服务器（不指定数据库）
        conn = pymysql.connect(
            host=DB_HOST,
            port=int(DB_PORT),
            user=DB_USER,
            password=DB_PASSWORD,
            charset='utf8mb4'
        )
        
        print("成功连接到MySQL服务器")
        
        # 检查数据库是否存在
        with conn.cursor() as cursor:
            cursor.execute(f"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{DB_NAME}'")
            result = cursor.fetchone()
            
            if result:
                print(f"数据库 {DB_NAME} 已存在")
            else:
                print(f"数据库 {DB_NAME} 不存在，正在创建...")
                cursor.execute(f"CREATE DATABASE {DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                conn.commit()
                print(f"数据库 {DB_NAME} 创建成功")
        
        # 关闭连接
        conn.close()
        
        # 连接到指定的数据库
        conn = pymysql.connect(
            host=DB_HOST,
            port=int(DB_PORT),
            user=DB_USER,
            password=DB_PASSWORD,
            database=DB_NAME,
            charset='utf8mb4'
        )
        
        print(f"成功连接到数据库 {DB_NAME}")
        
        # 检查表是否存在
        with conn.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            if tables:
                print("数据库中的表:")
                for table in tables:
                    print(f"- {table[0]}")
            else:
                print("数据库中没有表")
        
        # 关闭连接
        conn.close()
        
        print("MySQL连接测试成功")
        return True
    
    except Exception as e:
        print(f"MySQL连接测试失败: {str(e)}")
        return False

def test_sqlalchemy_connection():
    """测试SQLAlchemy连接"""
    print("测试SQLAlchemy连接...")
    
    try:
        from database.db_manager import engine, Base, session_scope
        from database.models import User, Vehicle, ParkingRecord
        
        # 测试连接
        with engine.connect() as connection:
            print("SQLAlchemy连接成功")
        
        # 测试会话
        with session_scope() as session:
            # 查询用户数量
            user_count = session.query(User).count()
            print(f"用户数量: {user_count}")
            
            # 查询车辆数量
            vehicle_count = session.query(Vehicle).count()
            print(f"车辆数量: {vehicle_count}")
            
            # 查询停车记录数量
            record_count = session.query(ParkingRecord).count()
            print(f"停车记录数量: {record_count}")
        
        print("SQLAlchemy测试成功")
        return True
    
    except Exception as e:
        print(f"SQLAlchemy测试失败: {str(e)}")
        return False

def test_create_sample_data():
    """测试创建示例数据"""
    print("测试创建示例数据...")
    
    try:
        from database.db_manager import session_scope
        from database.models import User, Vehicle, ParkingRecord, Member
        
        with session_scope() as session:
            # 创建测试用户
            test_user = User(
                username='test_user',
                email='<EMAIL>',
                is_admin=False
            )
            test_user.set_password('password123')
            session.add(test_user)
            print("测试用户创建成功")
            
            # 创建测试会员
            test_member = Member(
                name='测试会员',
                phone='13800138000',
                membership_type='普通会员',
                discount_rate=0.9
            )
            session.add(test_member)
            session.flush()  # 获取ID
            print("测试会员创建成功")
            
            # 创建测试车辆
            test_vehicle = Vehicle(
                plate_number='京A12345',
                vehicle_type='小型轿车',
                member_id=test_member.id
            )
            session.add(test_vehicle)
            session.flush()
            print("测试车辆创建成功")
            
            # 创建测试停车记录
            entry_time = datetime.now()
            test_record = ParkingRecord(
                plate_number='京A12345',
                entry_time=entry_time,
                vehicle_id=test_vehicle.id
            )
            session.add(test_record)
            print("测试停车记录创建成功")
        
        print("示例数据创建成功")
        return True
    
    except Exception as e:
        print(f"创建示例数据失败: {str(e)}")
        return False

if __name__ == '__main__':
    print("开始MySQL数据库测试...")
    
    # 测试MySQL连接
    if test_mysql_connection():
        # 测试SQLAlchemy连接
        if test_sqlalchemy_connection():
            # 测试创建示例数据
            test_create_sample_data()
    
    print("MySQL数据库测试完成")
