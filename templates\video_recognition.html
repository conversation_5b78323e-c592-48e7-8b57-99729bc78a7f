<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时视频识别 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">实时视频识别</h1>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="row">
                    <div class="col-lg-8 mx-auto">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-primary">实时视频车牌识别</h6>
                                <div>
                                    <button id="startBtn" class="btn btn-success btn-sm">
                                        <i class="bi bi-play-fill"></i> 开始识别
                                    </button>
                                    <button id="stopBtn" class="btn btn-danger btn-sm" disabled>
                                        <i class="bi bi-stop-fill"></i> 停止识别
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="text-center mb-4">
                                    <div id="videoContainer" class="mb-3">
                                        <video id="video" width="640" height="480" autoplay class="border rounded"></video>
                                        <canvas id="canvas" width="640" height="480" style="display: none;"></canvas>
                                    </div>
                                    <div id="statusIndicator" class="mb-3">
                                        <span class="badge bg-secondary">未开始识别</span>
                                    </div>
                                </div>

                                <div class="card mb-4">
                                    <div class="card-header py-3">
                                        <h6 class="m-0 font-weight-bold text-primary">识别结果</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="recognitionResults" class="list-group">
                                            <div class="list-group-item text-center text-muted">
                                                暂无识别结果
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 手动输入车牌号 -->
                                <div class="card mb-4">
                                    <div class="card-header py-3">
                                        <h6 class="m-0 font-weight-bold text-primary">手动输入车牌号</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="input-group">
                                            <input type="text" id="manualPlateNumber" class="form-control" placeholder="例如：沪K·R9888">
                                            <button class="btn btn-success" type="button" id="submitManualPlate">
                                                <i class="bi bi-check-circle me-2"></i>提交
                                            </button>
                                        </div>
                                        <div class="form-text">如果自动识别失败，可以手动输入车牌号</div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <h5>使用说明：</h5>
                                    <ol>
                                        <li>点击"开始识别"按钮启动摄像头</li>
                                        <li>将车牌对准摄像头</li>
                                        <li>系统会自动识别车牌并记录</li>
                                        <li>识别结果会实时显示在下方</li>
                                        <li>首次识别为入场记录，再次识别同一车牌为出场记录</li>
                                        <li>如果自动识别失败，可以使用手动输入功能</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.getElementById('video');
            const canvas = document.getElementById('canvas');
            const startBtn = document.getElementById('startBtn');
            const stopBtn = document.getElementById('stopBtn');
            const statusIndicator = document.getElementById('statusIndicator');
            const recognitionResults = document.getElementById('recognitionResults');
            const manualPlateNumber = document.getElementById('manualPlateNumber');
            const submitManualPlate = document.getElementById('submitManualPlate');

            let stream = null;
            let isRecognizing = false;
            let recognitionInterval = null;

            // 开始识别
            startBtn.addEventListener('click', async function() {
                try {
                    stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    video.srcObject = stream;

                    startBtn.disabled = true;
                    stopBtn.disabled = false;
                    isRecognizing = true;

                    statusIndicator.innerHTML = '<span class="badge bg-success">正在识别中</span>';

                    // 每2秒捕获一帧并发送到服务器进行识别
                    recognitionInterval = setInterval(captureAndRecognize, 2000);
                } catch (err) {
                    console.error('无法访问摄像头:', err);
                    alert('无法访问摄像头，请确保摄像头已连接并授予权限。');
                }
            });

            // 停止识别
            stopBtn.addEventListener('click', function() {
                if (stream) {
                    stream.getTracks().forEach(track => track.stop());
                    video.srcObject = null;
                }

                startBtn.disabled = false;
                stopBtn.disabled = true;
                isRecognizing = false;

                statusIndicator.innerHTML = '<span class="badge bg-secondary">已停止识别</span>';

                clearInterval(recognitionInterval);
            });

            // 捕获视频帧并发送到服务器进行识别
            function captureAndRecognize() {
                if (!isRecognizing) return;

                const context = canvas.getContext('2d');
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                // 将Canvas转换为Blob
                canvas.toBlob(function(blob) {
                    const formData = new FormData();
                    formData.append('video_frame', blob, 'frame.jpg');

                    // 发送到服务器进行识别
                    fetch('/api/recognize_video', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.recognized) {
                            // 添加识别结果
                            addRecognitionResult(data);
                        } else if (data.recognized === false) {
                            // 未识别到车牌
                            if (data.need_manual_input) {
                                // 提示用户手动输入
                                updateStatus('未识别到车牌，请尝试手动输入', 'warning');
                                // 聚焦到手动输入框
                                manualPlateNumber.focus();
                            } else {
                                updateStatus('未识别到车牌', 'warning');
                            }
                        } else if (!data.success) {
                            // 识别失败
                            updateStatus(data.error || '识别失败', 'danger');
                        }
                    })
                    .catch(error => {
                        console.error('识别请求失败:', error);
                        updateStatus('识别请求失败', 'danger');
                    });
                }, 'image/jpeg', 0.95);
            }

            // 更新状态指示器
            function updateStatus(message, type = 'secondary') {
                statusIndicator.innerHTML = `<span class="badge bg-${type}">${message}</span>`;

                // 如果是警告或错误，2秒后恢复为"正在识别中"
                if (type === 'warning' || type === 'danger') {
                    setTimeout(() => {
                        if (isRecognizing) {
                            statusIndicator.innerHTML = '<span class="badge bg-success">正在识别中</span>';
                        }
                    }, 2000);
                }
            }

            // 处理手动输入的车牌号
            function processManualPlateNumber() {
                const plateNumber = manualPlateNumber.value.trim();
                if (!plateNumber) {
                    alert('请输入车牌号');
                    return;
                }

                // 更新状态
                updateStatus('正在处理手动输入的车牌号...', 'info');

                // 发送到服务器进行处理
                const formData = new FormData();
                formData.append('manual_plate_number', plateNumber);

                fetch('/api/recognize_video', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.recognized) {
                        // 添加识别结果
                        addRecognitionResult(data);
                        // 清空输入框
                        manualPlateNumber.value = '';
                    } else if (!data.success) {
                        // 处理失败
                        updateStatus(data.error || '处理失败', 'danger');
                    }
                })
                .catch(error => {
                    console.error('处理请求失败:', error);
                    updateStatus('处理请求失败', 'danger');
                });
            }

            // 点击提交按钮处理手动输入
            submitManualPlate.addEventListener('click', processManualPlateNumber);

            // 按回车键也可以提交
            manualPlateNumber.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    processManualPlateNumber();
                }
            });

            // 添加识别结果到列表
            function addRecognitionResult(data) {
                // 清除"暂无识别结果"提示
                if (recognitionResults.querySelector('.text-muted')) {
                    recognitionResults.innerHTML = '';
                }

                const now = new Date();
                const timeString = now.toLocaleTimeString();

                const resultItem = document.createElement('div');
                resultItem.className = 'list-group-item';

                if (data.action === 'entry') {
                    resultItem.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${data.plate_number}</strong>
                                <span class="badge bg-primary ms-2">入场</span>
                            </div>
                            <small>${timeString}</small>
                        </div>
                        <div class="mt-1">
                            <small>入场时间: ${data.entry_time}</small>
                        </div>
                    `;

                    // 更新状态
                    updateStatus(`已识别到车牌 ${data.plate_number} 入场`, 'primary');
                } else {
                    resultItem.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${data.plate_number}</strong>
                                <span class="badge bg-success ms-2">出场</span>
                            </div>
                            <small>${timeString}</small>
                        </div>
                        <div class="mt-1">
                            <small>入场: ${data.entry_time}</small><br>
                            <small>出场: ${data.exit_time}</small><br>
                            <small>停车时长: ${data.duration} 小时</small><br>
                            <small>费用: ${data.fee} 元</small>
                        </div>
                    `;

                    // 更新状态
                    updateStatus(`已识别到车牌 ${data.plate_number} 出场`, 'success');
                }

                // 将新结果添加到顶部
                recognitionResults.insertBefore(resultItem, recognitionResults.firstChild);

                // 限制显示最近10条记录
                const items = recognitionResults.querySelectorAll('.list-group-item');
                if (items.length > 10) {
                    recognitionResults.removeChild(items[items.length - 1]);
                }
            }
        });
    </script>
</body>
</html>
