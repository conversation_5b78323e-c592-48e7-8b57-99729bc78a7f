<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理仪表板 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('qrcode.upload_qrcode') }}">
                                <i class="bi bi-qr-code me-2"></i>
                                上传支付码
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">管理仪表板</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <span class="btn btn-sm btn-outline-secondary" id="current-date-display">
                                <i class="bi bi-calendar3"></i>
                                <span id="current-date"></span>
                            </span>
                        </div>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 状态卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            总车位数</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ status.total_spaces }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-p-square-fill fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            可用车位</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ status.available_spaces }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-check-square-fill fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            已占车位</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ status.occupied_spaces }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-car-front-fill fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            占用率</div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            {{ (status.occupied_spaces / status.total_spaces * 100)|round(1) }}%
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-percent fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近停车记录 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">最近停车记录</h6>
                        <a href="{{ url_for('view_records') }}" class="btn btn-sm btn-primary">
                            查看全部
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>车牌号</th>
                                        <th>入场时间</th>
                                        <th>出场时间</th>
                                        <th>停车时长</th>
                                        <th>费用</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in records %}
                                    <tr>
                                        <td>{{ record.plate_number }}</td>
                                        <td>{{ record.entry_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        <td>
                                            {% if record.exit_time %}
                                                {{ record.exit_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                            {% else %}
                                                <span class="badge bg-primary">在场</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.duration %}
                                                {{ record.duration }} 小时
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.fee %}
                                                {{ record.fee }} 元
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.exit_time %}
                                                {% if record.paid %}
                                                    <span class="badge bg-success">已支付</span>
                                                {% else %}
                                                    <span class="badge bg-warning">未支付</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-info">停车中</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">快捷操作</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <a href="{{ url_for('recognize_upload') }}" class="btn btn-primary btn-block w-100 py-3">
                                            <i class="bi bi-upload me-2"></i>上传图片识别
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a href="{{ url_for('recognize_video') }}" class="btn btn-success btn-block w-100 py-3">
                                            <i class="bi bi-camera-video me-2"></i>实时视频识别
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a href="{{ url_for('view_members') }}" class="btn btn-info btn-block w-100 py-3">
                                            <i class="bi bi-person-plus me-2"></i>会员管理
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a href="{{ url_for('view_reports') }}" class="btn btn-warning btn-block w-100 py-3">
                                            <i class="bi bi-graph-up me-2"></i>查看报表
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">系统信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h5>当前收费标准</h5>
                                    <p>每小时：{{ fee.hourly_rate }} 元</p>
                                    <p>每日最高：{{ fee.daily_max }} 元</p>
                                    <p>免费时间：{{ fee.free_minutes }} 分钟</p>
                                </div>
                                <div>
                                    <h5>会员信息</h5>
                                    <p>活跃会员数：{{ member_count }}</p>
                                    <a href="{{ url_for('view_members') }}" class="btn btn-sm btn-outline-primary">
                                        管理会员
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
