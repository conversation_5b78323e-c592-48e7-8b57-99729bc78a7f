<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 基本元数据设置 -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车记录 - 智能停车场管理系统</title>

    <!-- 引入外部样式表 -->
    <!-- Bootstrap 5 CSS 框架 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <!-- Bootstrap 图标库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <!-- 自定义仪表板样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏导航 - 提供系统主要功能的导航链接 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <!-- 系统标题 -->
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <!-- 导航菜单 -->
                    <ul class="nav flex-column">
                        <!-- 仪表板 - 系统概览页面 -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <!-- 上传识别 - 上传图片进行车牌识别 -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <!-- 实时识别 - 通过摄像头实时识别车牌 -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <!-- 停车记录 - 当前页面，显示为激活状态 -->
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <!-- 会员管理 - 管理会员信息和权限 -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <!-- 收费设置 - 配置停车费率和规则 -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <!-- 统计报表 - 查看系统统计数据和报表 -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <!-- 退出登录 - 退出系统 -->
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 - 显示停车记录管理的主要功能 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 页面标题和操作按钮 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">停车记录管理</h1>
                    <!-- 功能按钮组 -->
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <!-- 导出CSV按钮 - 将当前筛选的记录导出为CSV文件 -->
                        <a href="{{ url_for('export_all_records') }}{% if request.args.get('plate_number') %}?plate_number={{ request.args.get('plate_number') }}{% endif %}" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-file-earmark-arrow-down me-1"></i>
                            导出CSV
                        </a>
                        <!-- 清空记录按钮 - 清空所有停车记录（会先导出备份） -->
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="confirmClearRecords()">
                            <i class="bi bi-trash me-1"></i>
                            清空记录
                        </button>
                    </div>
                </div>

                <!-- 显示Flask闪现消息 - 用于显示操作结果的提示信息 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 搜索和筛选卡片 - 提供多条件查询功能 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">搜索和筛选</h6>
                    </div>
                    <div class="card-body">
                        <!-- 搜索表单 - 使用GET方法提交，便于分享和书签 -->
                        <form id="searchForm" method="get" class="row g-3">
                            <!-- 车牌号搜索字段 -->
                            <div class="col-md-3">
                                <label for="plate_number" class="form-label">车牌号</label>
                                <input type="text" class="form-control" id="plate_number" name="plate_number" value="{{ request.args.get('plate_number', '') }}" placeholder="输入完整或部分车牌号">
                            </div>
                            <!-- 开始日期筛选 -->
                            <div class="col-md-3">
                                <label for="start_date" class="form-label">开始日期</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.args.get('start_date', '') }}">
                                    <button type="button" class="btn btn-outline-secondary" id="set-today-start" title="设置为今天">
                                        <i class="bi bi-calendar-check"></i>
                                    </button>
                                </div>
                            </div>
                            <!-- 结束日期筛选 -->
                            <div class="col-md-3">
                                <label for="end_date" class="form-label">结束日期</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.args.get('end_date', '') }}">
                                    <button type="button" class="btn btn-outline-secondary" id="set-today-end" title="设置为今天">
                                        <i class="bi bi-calendar-check"></i>
                                    </button>
                                </div>
                            </div>
                            <!-- 状态筛选下拉框 -->
                            <div class="col-md-3">
                                <label for="status" class="form-label">状态</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">全部</option>
                                    <option value="in" {% if request.args.get('status') == 'in' %}selected{% endif %}>在场</option>
                                    <option value="out" {% if request.args.get('status') == 'out' %}selected{% endif %}>已出场</option>
                                    <option value="paid" {% if request.args.get('status') == 'paid' %}selected{% endif %}>已支付</option>
                                    <option value="unpaid" {% if request.args.get('status') == 'unpaid' %}selected{% endif %}>未支付</option>
                                </select>
                            </div>
                            <!-- 表单按钮组 -->
                            <div class="col-12 text-end">
                                <!-- 搜索按钮 -->
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search me-1"></i>搜索
                                </button>
                                <!-- 重置按钮 - 清除所有筛选条件 -->
                                <button type="button" class="btn btn-secondary" id="reset-form">
                                    <i class="bi bi-arrow-counterclockwise me-1"></i>重置
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 停车记录表格 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">停车记录列表</h6>
                        <span class="badge bg-primary">共 {{ records.total }} 条记录</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>车牌号</th>
                                        <th>入场时间</th>
                                        <th>出场时间</th>
                                        <th>停车时长</th>
                                        <th>费用</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in records.items %}
                                    <tr>
                                        <td>{{ record.id }}</td>
                                        <td>{{ record.plate_number }}</td>
                                        <td>{{ record.entry_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        <td>
                                            {% if record.exit_time %}
                                                {{ record.exit_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                            {% else %}
                                                <span class="badge bg-primary">在场</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.duration %}
                                                {{ record.duration }} 小时
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.fee %}
                                                {{ record.fee }} 元
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if record.exit_time %}
                                                {% if record.paid %}
                                                    <span class="badge bg-success">已支付</span>
                                                {% else %}
                                                    <span class="badge bg-warning">未支付</span>
                                                {% endif %}
                                            {% else %}
                                                <span class="badge bg-info">停车中</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-info" onclick="viewDetails('{{ record.id }}')">
                                                <i class="bi bi-eye"></i>
                                            </button>

                                            {% if not record.exit_time %}
                                            <!-- 手动出库按钮 -->
                                            <button type="button" class="btn btn-sm btn-warning" onclick="manualExit('{{ record.id }}')">
                                                <i class="bi bi-box-arrow-right"></i> 手动出库
                                            </button>
                                            {% endif %}

                                            {% if record.exit_time and not record.paid %}
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-sm btn-success" onclick="markAsPaid('{{ record.id }}')">
                                                    <i class="bi bi-check-circle"></i> 标记支付
                                                </button>
                                                <button type="button" class="btn btn-sm btn-primary" onclick="showQRCodePayment('{{ record.id }}', '{{ record.fee }}')">
                                                    <i class="bi bi-qr-code"></i> 扫码支付
                                                </button>
                                            </div>
                                            {% endif %}

                                            <!-- 删除按钮 -->
                                            <button type="button" class="btn btn-sm btn-danger mt-1" onclick="confirmDeleteRecord('{{ record.id }}')">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center mt-4">
                                {% if records.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('view_records', page=records.prev_num, plate_number=request.args.get('plate_number', ''), start_date=request.args.get('start_date', ''), end_date=request.args.get('end_date', ''), status=request.args.get('status', '')) }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for page_num in records.iter_pages(left_edge=2, right_edge=2, left_current=2, right_current=2) %}
                                    {% if page_num %}
                                        {% if page_num == records.page %}
                                        <li class="page-item active">
                                            <a class="page-link" href="#">{{ page_num }}</a>
                                        </li>
                                        {% else %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('view_records', page=page_num, plate_number=request.args.get('plate_number', ''), start_date=request.args.get('start_date', ''), end_date=request.args.get('end_date', ''), status=request.args.get('status', '')) }}">{{ page_num }}</a>
                                        </li>
                                        {% endif %}
                                    {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#">...</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                {% if records.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('view_records', page=records.next_num, plate_number=request.args.get('plate_number', ''), start_date=request.args.get('start_date', ''), end_date=request.args.get('end_date', ''), status=request.args.get('status', '')) }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailsModalLabel">停车记录详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="detailsModalBody">
                    <!-- 详情内容将通过JavaScript动态填充 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 支付模态框 -->
    <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentModalLabel">标记为已支付</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="paymentForm">
                        <input type="hidden" id="record_id" name="record_id">
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">支付方式</label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="">请选择支付方式</option>
                                <option value="现金">现金</option>
                                <option value="微信">微信</option>
                                <option value="支付宝">支付宝</option>
                                <option value="银行卡">银行卡</option>
                                <option value="会员账户">会员账户</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="payment_amount" class="form-label">支付金额</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="payment_amount" name="payment_amount" step="0.01" required>
                                <span class="input-group-text">元</span>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitPayment()">确认支付</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 二维码支付模态框 -->
    <div class="modal fade" id="qrcodePaymentModal" tabindex="-1" aria-labelledby="qrcodePaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="qrcodePaymentModalLabel">扫码支付</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="qrcode_record_id">
                    <div class="row">
                        <div class="col-md-6 text-center mb-3">
                            <h4>微信支付</h4>
                            <div class="qrcode-container">
                                <img id="wechatQRCode" src="/static/img/your_wechat_qrcode.jpg" width="200" height="200" class="img-thumbnail" alt="微信支付二维码">
                                <p class="mt-2">请使用微信扫描上方二维码</p>
                            </div>
                        </div>
                        <div class="col-md-6 text-center mb-3">
                            <h4>支付宝支付</h4>
                            <div class="qrcode-container">
                                <img id="alipayQRCode" src="/static/img/your_alipay_qrcode.jpg" width="200" height="200" class="img-thumbnail" alt="支付宝支付二维码">
                                <p class="mt-2">请使用支付宝扫描上方二维码</p>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info text-center">
                        <p>请使用微信或支付宝扫描上方二维码进行支付</p>
                        <p>支付金额: <span id="qrcode_amount" class="fw-bold">¥0.00</span></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-success" onclick="confirmQRCodePayment()">已完成支付</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 手动出库模态框 -->
    <div class="modal fade" id="manualExitModal" tabindex="-1" aria-labelledby="manualExitModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="manualExitModalLabel">手动出库</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="manualExitForm">
                        <input type="hidden" id="exit_record_id" name="record_id">
                        <div class="mb-3">
                            <label for="exit_time" class="form-label">出场时间</label>
                            <input type="datetime-local" class="form-control" id="exit_time" name="exit_time" required>
                        </div>
                        <div class="mb-3">
                            <label for="exit_note" class="form-label">备注</label>
                            <textarea class="form-control" id="exit_note" name="exit_note" rows="3" placeholder="请输入手动出库原因..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="confirmManualExit()">确认出库</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 定义全局变量，用于JavaScript函数中的URL -->
    <script>
        // 定义URL变量，用于JavaScript函数中
        const recordsUrl = '{{ url_for("view_records") }}';
        const deleteRecordUrl = '{{ url_for("delete_parking_record_route", record_id=0) }}';

        // 构建导出URL
        const exportRecordsUrl = '{{ url_for("export_all_records") }}{% if request.args.get("plate_number") %}?plate_number={{ request.args.get("plate_number") }}{% endif %}';
    </script>

    <!-- 引入停车记录管理的JavaScript文件 -->
    <script src="{{ url_for('static', filename='js/parking_records.js') }}"></script>
    <script src="{{ url_for('static', filename='js/records.js') }}"></script>

    <!-- 隐藏的清空记录表单 - 用于提交清空所有记录的请求 -->
    <form id="clearRecordsForm" action="{{ url_for('clear_parking_records_route') }}" method="POST" style="display: none;">
    </form>
</body>
</html>
