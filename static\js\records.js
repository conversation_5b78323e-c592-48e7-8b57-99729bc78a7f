/**
 * 停车记录页面的JavaScript功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 设置开始日期为今天的按钮
    const setTodayStartBtn = document.getElementById('set-today-start');
    if (setTodayStartBtn) {
        setTodayStartBtn.addEventListener('click', function() {
            const today = new Date();
            const formattedDate = formatDate(today);
            document.getElementById('start_date').value = formattedDate;
        });
    }

    // 设置结束日期为今天的按钮
    const setTodayEndBtn = document.getElementById('set-today-end');
    if (setTodayEndBtn) {
        setTodayEndBtn.addEventListener('click', function() {
            const today = new Date();
            const formattedDate = formatDate(today);
            document.getElementById('end_date').value = formattedDate;
        });
    }

    // 重置搜索表单按钮
    const resetFormBtn = document.getElementById('reset-form');
    if (resetFormBtn) {
        resetFormBtn.addEventListener('click', function() {
            document.getElementById('searchForm').reset();
        });
    }
});

/**
 * 格式化日期为YYYY-MM-DD格式
 * 
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 查看停车记录详情
 * 
 * @param {string} recordId - 停车记录ID
 */
function viewDetails(recordId) {
    // 将字符串ID转换为数字，确保API调用正确
    recordId = parseInt(recordId);
    
    // 获取详情模态框的内容区域
    const detailsModalBody = document.getElementById('detailsModalBody');
    
    // 显示加载中的提示信息
    detailsModalBody.innerHTML = `<p>正在加载记录 #${recordId} 的详细信息...</p>`;

    // 初始化并显示模态框
    const detailsModal = new bootstrap.Modal(document.getElementById('detailsModal'));
    detailsModal.show();

    // 发送AJAX请求获取记录详情
    fetch(`/api/records/${recordId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 构建详情HTML
                let html = `
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th>车牌号</th>
                                <td>${data.record.plate_number}</td>
                            </tr>
                            <tr>
                                <th>入场时间</th>
                                <td>${formatDateTime(data.record.entry_time)}</td>
                            </tr>
                            <tr>
                                <th>出场时间</th>
                                <td>${data.record.exit_time ? formatDateTime(data.record.exit_time) : '尚未出场'}</td>
                            </tr>
                            <tr>
                                <th>停车时长</th>
                                <td>${data.record.duration ? data.record.duration + ' 小时' : '尚未计算'}</td>
                            </tr>
                            <tr>
                                <th>费用</th>
                                <td>${data.record.fee ? data.record.fee + ' 元' : '尚未计算'}</td>
                            </tr>
                            <tr>
                                <th>支付状态</th>
                                <td>${data.record.paid ? '<span class="badge bg-success">已支付</span>' : '<span class="badge bg-warning">未支付</span>'}</td>
                            </tr>
                        </table>
                    </div>
                `;

                // 如果是会员，显示会员信息
                if (data.member) {
                    html += `
                        <div class="mt-4">
                            <h5>会员信息</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <tr>
                                        <th>会员姓名</th>
                                        <td>${data.member.name}</td>
                                    </tr>
                                    <tr>
                                        <th>会员类型</th>
                                        <td>${data.member.membership_type}</td>
                                    </tr>
                                    <tr>
                                        <th>联系电话</th>
                                        <td>${data.member.phone}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    `;
                }

                // 更新模态框内容
                detailsModalBody.innerHTML = html;
            } else {
                detailsModalBody.innerHTML = `<div class="alert alert-danger">加载记录详情失败: ${data.message}</div>`;
            }
        })
        .catch(error => {
            detailsModalBody.innerHTML = `<div class="alert alert-danger">请求出错: ${error.message}</div>`;
        });
}

/**
 * 格式化日期时间为可读格式
 * 
 * @param {string} dateTimeStr - ISO格式的日期时间字符串
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}
