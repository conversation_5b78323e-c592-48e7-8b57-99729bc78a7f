<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员管理 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h5 class="text-white">智能停车场管理系统</h5>
                    </div>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_upload') }}">
                                <i class="bi bi-upload me-2"></i>
                                上传识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('recognize_video') }}">
                                <i class="bi bi-camera-video me-2"></i>
                                实时识别
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_records') }}">
                                <i class="bi bi-list-check me-2"></i>
                                停车记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('view_members') }}">
                                <i class="bi bi-person-badge me-2"></i>
                                会员管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_fees') }}">
                                <i class="bi bi-cash-coin me-2"></i>
                                收费设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_reports') }}">
                                <i class="bi bi-graph-up me-2"></i>
                                统计报表
                            </a>
                        </li>
                        <li class="nav-item mt-5">
                            <a class="nav-link text-danger" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">会员管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-primary">
                            <i class="bi bi-person-plus me-1"></i>
                            添加会员
                        </button>
                    </div>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 会员列表 -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">会员列表</h6>
                        <span class="badge bg-primary">共 {{ members|length }} 名会员</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>姓名</th>
                                        <th>车牌号</th>
                                        <th>电话</th>
                                        <th>会员类型</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for member in members %}
                                    <tr>
                                        <td>{{ member.id }}</td>
                                        <td>{{ member.name }}</td>
                                        <td>{{ member.plate_number }}</td>
                                        <td>{{ member.phone }}</td>
                                        <td>{{ member.membership_type }}</td>
                                        <td>
                                            {% if member.is_active %}
                                                <span class="badge bg-success">活跃</span>
                                            {% else %}
                                                <span class="badge bg-secondary">非活跃</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-info" onclick="editMember({{ member.id }}, '{{ member.name }}', '{{ member.plate_number }}', '{{ member.phone }}', '{{ member.membership_type }}')">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="deleteMember({{ member.id }}, '{{ member.name }}')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 会员统计 -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">会员类型分布</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-pie pt-4 pb-2">
                                    <div class="text-center">
                                        <p>会员类型分布图表将在这里显示</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="card shadow mb-4">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">会员活跃状态</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-pie pt-4 pb-2">
                                    <div class="text-center">
                                        <p>会员活跃状态图表将在这里显示</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 会员编辑模态框 -->
    <div class="modal fade" id="editMemberModal" tabindex="-1" aria-labelledby="editMemberModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editMemberModalLabel">编辑会员</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editMemberForm">
                        <input type="hidden" id="member_id" name="member_id">
                        <div class="mb-3">
                            <label for="name" class="form-label">姓名</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="plate_number" class="form-label">车牌号</label>
                            <input type="text" class="form-control" id="plate_number" name="plate_number" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">电话</label>
                            <input type="text" class="form-control" id="phone" name="phone" required>
                        </div>
                        <div class="mb-3">
                            <label for="membership_type" class="form-label">会员类型</label>
                            <select class="form-select" id="membership_type" name="membership_type" required>
                                <option value="普通会员">普通会员</option>
                                <option value="VIP会员">VIP会员</option>
                                <option value="企业会员">企业会员</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveMember()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteMemberModal" tabindex="-1" aria-labelledby="deleteMemberModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteMemberModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除会员 <span id="deleteMemberName" class="fw-bold"></span> 吗？此操作不可撤销。</p>
                    <input type="hidden" id="deleteMemberId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 编辑会员
        function editMember(id, name, plateNumber, phone, membershipType) {
            document.getElementById('member_id').value = id;
            document.getElementById('name').value = name;
            document.getElementById('plate_number').value = plateNumber;
            document.getElementById('phone').value = phone;
            document.getElementById('membership_type').value = membershipType;

            // 修改模态框标题
            document.getElementById('editMemberModalLabel').textContent = '编辑会员';

            const modal = new bootstrap.Modal(document.getElementById('editMemberModal'));
            modal.show();
        }

        // 保存会员信息
        function saveMember() {
            const form = document.getElementById('editMemberForm');
            if (form.checkValidity()) {
                const id = document.getElementById('member_id').value;
                const name = document.getElementById('name').value;
                const plateNumber = document.getElementById('plate_number').value;
                const phone = document.getElementById('phone').value;
                const membershipType = document.getElementById('membership_type').value;

                // 创建表单数据
                const formData = new FormData();
                if (id) {
                    formData.append('member_id', id);
                    formData.append('name', name);
                    formData.append('plate_number', plateNumber);
                    formData.append('phone', phone);
                    formData.append('membership_type', membershipType);

                    // 发送AJAX请求更新会员
                    fetch('/api/members/update', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('editMemberModal'));
                            modal.hide();

                            // 显示成功消息
                            alert('会员信息已更新！');

                            // 刷新页面
                            location.reload();
                        } else {
                            alert(data.message || '更新失败，请重试');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('更新会员信息时发生错误，请重试');
                    });
                } else {
                    // 添加新会员
                    formData.append('name', name);
                    formData.append('plate_number', plateNumber);
                    formData.append('phone', phone);
                    formData.append('membership_type', membershipType);

                    // 发送AJAX请求添加会员
                    fetch('/api/members/add', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('editMemberModal'));
                            modal.hide();

                            // 显示成功消息
                            alert('会员添加成功！');

                            // 刷新页面
                            location.reload();
                        } else {
                            alert(data.message || '添加失败，请重试');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('添加会员时发生错误，请重试');
                    });
                }
            } else {
                form.reportValidity();
            }
        }

        // 删除会员
        function deleteMember(id, name) {
            document.getElementById('deleteMemberId').value = id;
            document.getElementById('deleteMemberName').textContent = name;

            const modal = new bootstrap.Modal(document.getElementById('deleteMemberModal'));
            modal.show();
        }

        // 确认删除
        function confirmDelete() {
            const id = document.getElementById('deleteMemberId').value;

            // 创建表单数据
            const formData = new FormData();
            formData.append('member_id', id);

            // 发送AJAX请求删除会员
            fetch('/api/members/delete', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('deleteMemberModal'));
                    modal.hide();

                    // 显示成功消息
                    alert('会员已删除！');

                    // 刷新页面
                    location.reload();
                } else {
                    alert(data.message || '删除失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除会员时发生错误，请重试');
            });
        }

        // 添加会员按钮
        document.addEventListener('DOMContentLoaded', function() {
            const addButton = document.querySelector('.btn-toolbar .btn-primary');
            addButton.addEventListener('click', function() {
                // 清空表单
                document.getElementById('member_id').value = '';
                document.getElementById('name').value = '';
                document.getElementById('plate_number').value = '';
                document.getElementById('phone').value = '';
                document.getElementById('membership_type').value = '普通会员';

                // 修改模态框标题
                document.getElementById('editMemberModalLabel').textContent = '添加会员';

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('editMemberModal'));
                modal.show();
            });
        });
    </script>
</body>
</html>
