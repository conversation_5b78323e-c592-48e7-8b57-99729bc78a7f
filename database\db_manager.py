#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库管理模块 - MySQL版本
"""

import os
import sys
import pymysql
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from contextlib import contextmanager
from flask import current_app

# 从配置文件获取数据库连接信息
def get_engine():
    """根据配置创建数据库引擎"""
    try:
        # 从Flask应用配置获取数据库URI
        if current_app:
            db_uri = current_app.config['SQLALCHEMY_DATABASE_URI']
        else:
            # 如果不在Flask上下文中，从config.py导入
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from config import SQLALCHEMY_DATABASE_URI
            db_uri = SQLALCHEMY_DATABASE_URI

        # 创建数据库引擎
        return create_engine(
            db_uri,
            pool_recycle=3600,  # 连接池回收时间
            pool_pre_ping=True,  # 连接前ping一下，确保连接有效
            echo=current_app.config.get('SQLALCHEMY_ECHO', False) if current_app else False
        )
    except Exception as e:
        print(f"数据库连接错误: {str(e)}")
        raise

# 创建数据库会话
engine = get_engine()
db_session = scoped_session(sessionmaker(autocommit=False, autoflush=False, bind=engine))
Base = declarative_base()
Base.query = db_session.query_property()

@contextmanager
def session_scope():
    """提供事务范围的上下文管理器"""
    session = db_session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        print(f"数据库事务错误: {str(e)}")
        raise
    finally:
        session.close()

def create_database_if_not_exists():
    """检查并创建数据库（如果不存在）"""
    try:
        # 从配置获取数据库连接信息
        if current_app:
            db_user = current_app.config.get('DB_USER', 'root')
            db_password = current_app.config.get('DB_PASSWORD', '')
            db_host = current_app.config.get('DB_HOST', 'localhost')
            db_port = int(current_app.config.get('DB_PORT', 3306))
            db_name = current_app.config.get('DB_NAME', 'car_park')
        else:
            # 如果不在Flask上下文中，从config.py导入
            sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            from config import DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME
            db_user = DB_USER
            db_password = DB_PASSWORD
            db_host = DB_HOST
            db_port = int(DB_PORT)
            db_name = DB_NAME

        # 连接到MySQL服务器（不指定数据库）
        conn = pymysql.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_password,
            charset='utf8mb4'
        )

        try:
            with conn.cursor() as cursor:
                # 检查数据库是否存在
                cursor.execute(f"SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{db_name}'")
                result = cursor.fetchone()

                # 如果数据库不存在，则创建
                if not result:
                    print(f"创建数据库 {db_name}...")
                    cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                    conn.commit()
                    print(f"数据库 {db_name} 创建成功")
                else:
                    print(f"数据库 {db_name} 已存在")
        finally:
            conn.close()
    except Exception as e:
        print(f"创建数据库时出错: {str(e)}")
        raise

def init_db():
    """初始化数据库表结构"""
    try:
        # 首先确保数据库存在
        create_database_if_not_exists()

        # 导入模型定义
        import database.models

        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("数据库表结构初始化成功")
    except Exception as e:
        print(f"初始化数据库表结构时出错: {str(e)}")
        raise
