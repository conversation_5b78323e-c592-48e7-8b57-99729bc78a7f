#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
停车场管理系统简化版启动脚本
"""

import os
import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
import data_store

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['UPLOAD_FOLDER'] = 'uploads'

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 注册二维码上传路由
from qrcode_routes import qrcode_bp
app.register_blueprint(qrcode_bp, url_prefix='/qrcode')

# 模拟数据
class User:
    def __init__(self, id, username, password):
        self.id = id
        self.username = username
        self.password = password
        self.is_authenticated = False

    def check_password(self, password):
        return self.password == password

# 创建模拟用户
admin_user = User(1, 'admin', 'admin123')

# 首页
@app.route('/')
def index():
    return render_template('index.html')

# 登录页面
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        if username == admin_user.username and admin_user.check_password(password):
            session['user_id'] = admin_user.id
            admin_user.is_authenticated = True
            flash('登录成功！', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误！', 'danger')

    return render_template('login.html')

# 登出
@app.route('/logout')
def logout():
    session.pop('user_id', None)
    admin_user.is_authenticated = False
    flash('您已成功登出！', 'success')
    return redirect(url_for('index'))

# 检查登录状态
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录！', 'danger')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

# 管理员仪表板
@app.route('/dashboard')
@login_required
def dashboard():
    # 获取停车记录
    parking_records = data_store.get_parking_records()
    # 获取最近的停车记录
    recent_records = parking_records[:5] if parking_records else []
    # 获取当前停车场状态
    parking_status = {
        'total_spaces': 100,  # 假设总车位数为100
        'occupied_spaces': sum(1 for record in parking_records if record['exit_time'] is None),
        'available_spaces': 100 - sum(1 for record in parking_records if record['exit_time'] is None)
    }
    # 获取收费设置
    fee_settings = data_store.get_fee_settings()
    # 获取会员数量
    members = data_store.get_members()
    member_count = len(members)

    return render_template('dashboard.html',
                          records=recent_records,
                          status=parking_status,
                          now=datetime.datetime.now(),
                          fee=fee_settings,
                          member_count=member_count)

# 车牌识别 - 上传图片
@app.route('/recognize/upload', methods=['GET', 'POST'])
@login_required
def recognize_upload():
    if request.method == 'POST':
        if 'plate_image' not in request.files:
            flash('没有选择文件', 'danger')
            return redirect(request.url)

        file = request.files['plate_image']
        if file.filename == '':
            flash('没有选择文件', 'danger')
            return redirect(request.url)

        if file:
            # 保存上传的图片
            filename = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
            file.save(filename)

            # 调用车牌识别API进行真实识别
            try:
                # 导入车牌识别模块
                from recognition.plate_recognizer import recognize_plate_from_image

                # 进行真实的车牌识别
                plate_number = recognize_plate_from_image(filename)

                # 如果没有识别到车牌
                if not plate_number:
                    flash('未能识别到车牌，请重新上传清晰的车牌图片', 'warning')
                    return render_template('upload_image.html')
            except Exception as e:
                # 识别过程中出现错误
                print(f"车牌识别错误: {str(e)}")
                flash(f'车牌识别过程出错: {str(e)}', 'danger')
                return render_template('upload_image.html')

            # 获取停车记录
            parking_records = data_store.get_parking_records()

            # 检查是否已有入场记录
            existing_record = None
            for r in parking_records:
                if r['plate_number'] == plate_number and r['exit_time'] is None:
                    existing_record = r
                    break

            if existing_record:
                # 已有入场记录，这是出场
                # 获取收费设置
                fee_settings = data_store.get_fee_settings()
                hourly_rate = fee_settings['hourly_rate']

                # 计算停车时长和费用
                exit_time = datetime.datetime.now()
                duration = (exit_time - existing_record['entry_time']).total_seconds() / 3600
                duration_hours = round(duration, 2)
                fee = round(duration * hourly_rate, 2)  # 根据每小时费率计算

                # 更新记录
                updates = {
                    'exit_time': exit_time,
                    'duration': duration_hours,
                    'fee': fee
                }
                record = data_store.update_parking_record(existing_record['id'], updates)

                flash(f'识别成功！车牌号: {plate_number}，已记录出场', 'success')
            else:
                # 没有入场记录，这是入场
                new_record = {
                    'plate_number': plate_number,
                    'entry_time': datetime.datetime.now(),
                    'exit_time': None,
                    'duration': None,
                    'fee': None,
                    'paid': False
                }
                record = data_store.add_parking_record(new_record)

                flash(f'识别成功！车牌号: {plate_number}，已记录入场', 'success')

            return render_template('recognition_result.html', plate_number=plate_number, record=record)

    return render_template('upload_image.html')

# Tesseract OCR 安装指南
@app.route('/tesseract_guide')
def tesseract_guide():
    return render_template('tesseract_install.html')

# 车牌识别 - 实时视频
@app.route('/recognize/video')
@login_required
def recognize_video():
    return render_template('video_recognition.html')

# 车辆记录管理
@app.route('/records')
@login_required
def view_records():
    # 模拟分页
    class Pagination:
        def __init__(self, items, page, per_page, total):
            self.items = items
            self.page = page
            self.per_page = per_page
            self.total = total

        @property
        def pages(self):
            return max(1, self.total // self.per_page + (1 if self.total % self.per_page > 0 else 0))

        @property
        def has_prev(self):
            return self.page > 1

        @property
        def has_next(self):
            return self.page < self.pages

        @property
        def prev_num(self):
            return self.page - 1 if self.has_prev else None

        @property
        def next_num(self):
            return self.page + 1 if self.has_next else None

        def iter_pages(self, left_edge=2, right_edge=2, left_current=2, right_current=2):
            last = 0
            for num in range(1, self.pages + 1):
                if num <= left_edge or \
                   (num > self.page - left_current - 1 and num < self.page + right_current) or \
                   num > self.pages - right_edge:
                    if last + 1 != num:
                        yield None
                    yield num
                    last = num

    # 获取停车记录
    parking_records = data_store.get_parking_records()

    # 获取筛选参数
    plate_number = request.args.get('plate_number', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    status = request.args.get('status', '')

    # 筛选记录
    filtered_records = []
    for record in parking_records:
        # 车牌号筛选
        if plate_number and plate_number not in record['plate_number']:
            continue

        # 日期筛选
        if start_date:
            try:
                start_datetime = datetime.datetime.strptime(start_date, '%Y-%m-%d')
                if record['entry_time'] < start_datetime:
                    continue
            except ValueError:
                pass

        if end_date:
            try:
                end_datetime = datetime.datetime.strptime(end_date, '%Y-%m-%d')
                end_datetime = end_datetime + datetime.timedelta(days=1)  # 包含结束日期
                if record['entry_time'] > end_datetime:
                    continue
            except ValueError:
                pass

        # 状态筛选
        if status == 'in' and record['exit_time'] is not None:
            continue
        elif status == 'out' and record['exit_time'] is None:
            continue
        elif status == 'paid' and (not record.get('paid', False)):
            continue
        elif status == 'unpaid' and (record.get('paid', False) or record['exit_time'] is None):
            continue

        filtered_records.append(record)

    # 按入场时间倒序排序
    filtered_records.sort(key=lambda x: x['entry_time'], reverse=True)

    page = request.args.get('page', 1, type=int)
    per_page = 20
    total = len(filtered_records)

    # 简单分页
    start = (page - 1) * per_page
    end = min(start + per_page, total)
    items = filtered_records[start:end]

    pagination = Pagination(items, page, per_page, total)

    return render_template('records.html', records=pagination)







# 会员管理
@app.route('/members')
@login_required
def view_members():
    # 获取会员数据
    members = data_store.get_members()
    return render_template('members.html', members=members)

# 添加会员
@app.route('/api/members/add', methods=['POST'])
@login_required
def add_member():
    name = request.form.get('name')
    plate_number = request.form.get('plate_number')
    phone = request.form.get('phone')
    membership_type = request.form.get('membership_type')

    if not all([name, plate_number, phone, membership_type]):
        return jsonify({'success': False, 'message': '所有字段都是必填的'})

    new_member = data_store.add_member(name, plate_number, phone, membership_type)
    return jsonify({'success': True, 'member': new_member})

# 更新会员
@app.route('/api/members/update', methods=['POST'])
@login_required
def update_member():
    member_id = request.form.get('member_id', type=int)
    name = request.form.get('name')
    plate_number = request.form.get('plate_number')
    phone = request.form.get('phone')
    membership_type = request.form.get('membership_type')

    if not all([member_id, name, plate_number, phone, membership_type]):
        return jsonify({'success': False, 'message': '所有字段都是必填的'})

    success = data_store.update_member(member_id, name, plate_number, phone, membership_type)
    return jsonify({'success': success})

# 删除会员
@app.route('/api/members/delete', methods=['POST'])
@login_required
def delete_member():
    member_id = request.form.get('member_id', type=int)

    if not member_id:
        return jsonify({'success': False, 'message': '会员ID是必需的'})

    success = data_store.delete_member(member_id)
    return jsonify({'success': success})

# 收费标准设置
@app.route('/fees', methods=['GET', 'POST'])
@login_required
def manage_fees():
    if request.method == 'POST':
        hourly_rate = request.form.get('hourly_rate', type=float)
        daily_max = request.form.get('daily_max', type=float)
        free_minutes = request.form.get('free_minutes', type=int)

        if all([hourly_rate, daily_max, free_minutes is not None]):
            data_store.update_fee_settings(hourly_rate, daily_max, free_minutes)
            flash('收费标准更新成功！', 'success')
        else:
            flash('请填写所有必填字段', 'danger')

    # 获取当前收费标准
    fee = data_store.get_fee_settings()

    # 获取折扣设置
    discount_settings = data_store.get_discount_settings()

    return render_template('manage_fees.html', fee=fee, discount_settings=discount_settings)

# 更新折扣设置
@app.route('/api/discount/update', methods=['POST'])
@login_required
def update_discount():
    normal_discount = request.form.get('normal_discount', type=float)
    normal_fee = request.form.get('normal_fee', type=float)
    vip_discount = request.form.get('vip_discount', type=float)
    vip_fee = request.form.get('vip_fee', type=float)
    corporate_discount = request.form.get('corporate_discount', type=float)
    corporate_fee = request.form.get('corporate_fee', type=float)

    if all([normal_discount, normal_fee, vip_discount, vip_fee, corporate_discount, corporate_fee]):
        data_store.update_discount_settings(
            normal_discount, normal_fee,
            vip_discount, vip_fee,
            corporate_discount, corporate_fee
        )
        return jsonify({'success': True})
    else:
        return jsonify({'success': False, 'message': '请填写所有必填字段'})

# 财务报表
@app.route('/reports')
@login_required
def view_reports():
    # 获取报表类型和日期范围
    report_type = request.args.get('type', 'daily')
    start_date_str = request.args.get('start_date', datetime.datetime.now().strftime('%Y-%m-%d'))
    end_date_str = request.args.get('end_date', datetime.datetime.now().strftime('%Y-%m-%d'))

    try:
        start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d')
        end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d')
        end_date = end_date + datetime.timedelta(days=1)  # 包含结束日期
    except ValueError:
        start_date = datetime.datetime.now()
        end_date = start_date + datetime.timedelta(days=1)

    # 获取停车记录
    parking_records = data_store.get_parking_records()

    # 筛选日期范围内的记录
    filtered_records = [r for r in parking_records if r['entry_time'] >= start_date and r['entry_time'] < end_date]

    # 计算总收入
    total_income = sum(r.get('fee', 0) for r in filtered_records if r.get('paid', False))

    # 计算车辆数量
    total_vehicles = len(filtered_records)

    # 计算平均停车时长
    durations = [r.get('duration', 0) for r in filtered_records if r.get('duration') is not None]
    avg_duration = sum(durations) / len(durations) if durations else 0

    # 获取会员数量
    members = data_store.get_members()
    member_count = len(members)

    # 按日期分组统计数据
    daily_data = []

    # 根据报表类型确定日期格式
    if report_type == 'daily':
        # 按小时统计
        date_format = '%Y-%m-%d %H:00'
        delta = datetime.timedelta(hours=1)
    elif report_type == 'weekly':
        # 按日统计
        date_format = '%Y-%m-%d'
        delta = datetime.timedelta(days=1)
    elif report_type == 'monthly':
        # 按日统计
        date_format = '%Y-%m-%d'
        delta = datetime.timedelta(days=1)
    else:  # yearly
        # 按月统计
        date_format = '%Y-%m'
        delta = datetime.timedelta(days=30)  # 近似一个月

    # 创建日期范围
    current_date = start_date
    while current_date < end_date:
        date_str = current_date.strftime(date_format)

        # 筛选当前日期的记录
        if report_type == 'daily':
            # 按小时统计
            date_records = [r for r in filtered_records if r['entry_time'].strftime(date_format) == date_str]
        elif report_type in ['weekly', 'monthly']:
            # 按日统计
            date_records = [r for r in filtered_records if r['entry_time'].strftime(date_format) == date_str]
        else:  # yearly
            # 按月统计
            date_records = [r for r in filtered_records if r['entry_time'].strftime(date_format) == date_str]

        # 计算当前日期的收入和车辆数量
        date_income = sum(r.get('fee', 0) for r in date_records if r.get('paid', False))
        date_count = len(date_records)

        # 添加到日期数据中
        daily_data.append({
            'date': date_str,
            'income': round(date_income, 2),
            'count': date_count
        })

        # 移动到下一个日期
        current_date += delta

    # 生成图表
    charts = {}

    try:
        # 导入图表生成库
        import matplotlib
        # 设置matplotlib使用非交互式后端，避免在Web应用中出现GUI相关错误
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt
        # 设置中文字体支持
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        import io
        import base64

        if daily_data:
            # 收入趋势图
            plt.figure(figsize=(10, 6))
            dates = [item['date'] for item in daily_data]
            incomes = [item['income'] for item in daily_data]
            plt.plot(dates, incomes, marker='o')
            plt.title('收入趋势')
            plt.xlabel('日期')
            plt.ylabel('收入（元）')
            plt.grid(True)
            plt.xticks(rotation=45)
            plt.tight_layout()

            # 保存图表为base64编码的图像
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            income_chart = base64.b64encode(buffer.getvalue()).decode('utf-8')
            charts['income_trend'] = income_chart
            plt.close()
            buffer.close()

            # 车流量趋势图
            plt.figure(figsize=(10, 6))
            counts = [item['count'] for item in daily_data]
            plt.plot(dates, counts, marker='o', color='green')
            plt.title('车流量趋势')
            plt.xlabel('日期')
            plt.ylabel('车辆数量')
            plt.grid(True)
            plt.xticks(rotation=45)
            plt.tight_layout()

            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
            buffer.seek(0)
            vehicle_chart = base64.b64encode(buffer.getvalue()).decode('utf-8')
            charts['vehicle_trend'] = vehicle_chart
            plt.close()
            buffer.close()
    except Exception as e:
        print(f"生成图表时出错: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        # 确保所有图表都被关闭
        plt.close('all')

    # 查找最高收入日期
    max_income_date = None
    max_income_value = 0
    if daily_data:
        for item in daily_data:
            if item['income'] > max_income_value:
                max_income_value = item['income']
                max_income_date = item['date']

    # 查找最高车流量日期
    max_count_date = None
    max_count_value = 0
    if daily_data:
        for item in daily_data:
            if item['count'] > max_count_value:
                max_count_value = item['count']
                max_count_date = item['date']

    # 报表数据
    report_data = {
        'report_type': report_type,
        'start_date': start_date_str,
        'end_date': end_date_str,
        'total_income': round(total_income, 2),
        'total_vehicles': total_vehicles,
        'avg_duration': round(avg_duration, 2),
        'member_count': member_count,
        'max_income_date': max_income_date or datetime.datetime.now().strftime('%Y-%m-%d'),
        'max_income': round(max_income_value, 2),
        'max_count_date': max_count_date or datetime.datetime.now().strftime('%Y-%m-%d'),
        'max_count': max_count_value,
        'daily_data': daily_data,
        'charts': charts
    }

    return render_template('reports.html', report_data=report_data, report_type=report_type)

# 手动输入车牌号
@app.route('/manual_plate_input', methods=['POST'])
@login_required
def manual_plate_input():
    plate_number = request.form.get('plate_number')

    if not plate_number:
        flash('请输入车牌号', 'danger')
        return redirect(url_for('recognize_upload'))

    # 验证车牌号格式
    try:
        # 导入车牌验证函数
        from recognition.plate_recognizer import validate_plate_number

        # 验证车牌号格式
        valid_plate = validate_plate_number(plate_number)
        if not valid_plate:
            flash('车牌号格式不正确，请重新输入', 'warning')
            return redirect(url_for('recognize_upload'))
    except Exception as e:
        print(f"车牌验证错误: {str(e)}")
        # 如果验证函数不可用，则跳过验证
        valid_plate = plate_number

    # 获取停车记录
    parking_records = data_store.get_parking_records()

    # 检查是否已有入场记录
    existing_record = None
    for r in parking_records:
        if r['plate_number'] == valid_plate and r['exit_time'] is None:
            existing_record = r
            break

    if existing_record:
        # 已有入场记录，这是出场
        # 获取收费设置
        fee_settings = data_store.get_fee_settings()
        hourly_rate = fee_settings['hourly_rate']

        # 计算停车时长和费用
        exit_time = datetime.datetime.now()
        duration = (exit_time - existing_record['entry_time']).total_seconds() / 3600
        duration_hours = round(duration, 2)
        fee = round(duration * hourly_rate, 2)  # 根据每小时费率计算

        # 更新记录
        updates = {
            'exit_time': exit_time,
            'duration': duration_hours,
            'fee': fee
        }
        record = data_store.update_parking_record(existing_record['id'], updates)

        flash(f'车牌号: {valid_plate}，已记录出场', 'success')
    else:
        # 没有入场记录，这是入场
        new_record = {
            'plate_number': valid_plate,
            'entry_time': datetime.datetime.now(),
            'exit_time': None,
            'duration': None,
            'fee': None,
            'paid': False
        }
        record = data_store.add_parking_record(new_record)

        flash(f'车牌号: {valid_plate}，已记录入场', 'success')

    return render_template('recognition_result.html', plate_number=valid_plate, record=record)

# API端点 - 实时视频识别
@app.route('/api/recognize_video', methods=['POST'])
@login_required
def api_recognize_video():
    # 处理手动输入的车牌号
    if 'manual_plate_number' in request.form:
        plate_number = request.form.get('manual_plate_number')

        # 验证车牌号格式
        try:
            from recognition.plate_recognizer import validate_plate_number
            valid_plate = validate_plate_number(plate_number)
            if not valid_plate:
                return jsonify({
                    'success': False,
                    'error': '车牌号格式不正确',
                    'recognized': False
                })
            plate_number = valid_plate
        except Exception as e:
            print(f"车牌验证错误: {str(e)}")
            # 如果验证函数不可用，则使用原始输入

    # 处理上传的视频帧
    elif 'video_frame' in request.files:
        file = request.files['video_frame']
        filename = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_frame.jpg')
        file.save(filename)

        # 调用车牌识别API进行真实识别
        try:
            # 导入车牌识别模块
            from recognition.plate_recognizer import recognize_plate_from_image

            # 进行真实的车牌识别
            plate_number = recognize_plate_from_image(filename)

            # 如果没有识别到车牌
            if not plate_number:
                return jsonify({
                    'success': False,
                    'error': '未识别到车牌',
                    'recognized': False,
                    'need_manual_input': True  # 添加标志，提示前端需要手动输入
                })
        except Exception as e:
            # 识别过程中出现错误
            print(f"车牌识别错误: {str(e)}")
            return jsonify({
                'success': False,
                'error': f'车牌识别过程出错: {str(e)}',
                'recognized': False,
                'need_manual_input': True  # 添加标志，提示前端需要手动输入
            })
    else:
        return jsonify({
            'success': False,
            'error': '没有接收到视频帧或手动输入的车牌号',
            'recognized': False
        })

    # 获取停车记录
    parking_records = data_store.get_parking_records()

    # 检查是否已有入场记录
    existing_record = None
    for r in parking_records:
        if r['plate_number'] == plate_number and r['exit_time'] is None:
            existing_record = r
            break

    if existing_record:
        # 已有入场记录，这是出场
        # 获取收费设置
        fee_settings = data_store.get_fee_settings()
        hourly_rate = fee_settings['hourly_rate']

        # 计算停车时长和费用
        exit_time = datetime.datetime.now()
        duration = (exit_time - existing_record['entry_time']).total_seconds() / 3600
        duration_hours = round(duration, 2)
        fee = round(duration * hourly_rate, 2)  # 根据每小时费率计算

        # 更新记录
        updates = {
            'exit_time': exit_time,
            'duration': duration_hours,
            'fee': fee
        }
        record = data_store.update_parking_record(existing_record['id'], updates)

        return jsonify({
            'success': True,
            'recognized': True,
            'plate_number': plate_number,
            'action': 'exit',
            'entry_time': existing_record['entry_time'].strftime('%Y-%m-%d %H:%M:%S'),
            'exit_time': exit_time.strftime('%Y-%m-%d %H:%M:%S'),
            'duration': duration_hours,
            'fee': fee
        })
    else:
        # 没有入场记录，这是入场
        new_record = {
            'plate_number': plate_number,
            'entry_time': datetime.datetime.now(),
            'exit_time': None,
            'duration': None,
            'fee': None,
            'paid': False
        }
        record = data_store.add_parking_record(new_record)

        return jsonify({
            'success': True,
            'recognized': True,
            'plate_number': plate_number,
            'action': 'entry',
            'entry_time': record['entry_time'].strftime('%Y-%m-%d %H:%M:%S')
        })

# API端点 - 标记为已支付
@app.route('/api/payment/mark_paid', methods=['POST'])
@login_required
def mark_as_paid():
    record_id = request.form.get('record_id', type=int)
    payment_method = request.form.get('payment_method')
    payment_amount = request.form.get('payment_amount', type=float)

    if not all([record_id, payment_method, payment_amount]):
        return jsonify({'success': False, 'message': '所有字段都是必填的'})

    record = data_store.mark_record_as_paid(record_id, payment_method, payment_amount)
    if record:
        return jsonify({'success': True, 'record': record})
    else:
        return jsonify({'success': False, 'message': '找不到指定的记录'})

# API端点 - 手动出库
@app.route('/api/records/manual_exit', methods=['POST'])
@login_required
def manual_exit():
    record_id = request.form.get('record_id', type=int)
    exit_time_str = request.form.get('exit_time')
    exit_note = request.form.get('exit_note', '')

    if not all([record_id, exit_time_str]):
        return jsonify({'success': False, 'message': '记录ID和出场时间是必填的'})

    try:
        # 解析出场时间
        exit_time = datetime.datetime.fromisoformat(exit_time_str.replace('T', ' '))

        # 获取停车记录
        parking_records = data_store.get_parking_records()
        record = next((r for r in parking_records if r['id'] == record_id), None)

        if not record:
            return jsonify({'success': False, 'message': '找不到指定的记录'})

        if record['exit_time'] is not None:
            return jsonify({'success': False, 'message': '该记录已经出场'})

        # 检查出场时间是否晚于入场时间
        if exit_time <= record['entry_time']:
            return jsonify({'success': False, 'message': '出场时间必须晚于入场时间'})

        # 计算停车时长和费用
        duration = (exit_time - record['entry_time']).total_seconds() / 3600
        duration_hours = round(duration, 2)

        # 获取收费设置
        fee_settings = data_store.get_fee_settings()
        hourly_rate = fee_settings['hourly_rate']

        # 计算费用
        fee = round(duration * hourly_rate, 2)

        # 更新记录
        updates = {
            'exit_time': exit_time,
            'duration': duration_hours,
            'fee': fee,
            'exit_note': exit_note
        }

        updated_record = data_store.update_parking_record(record_id, updates)

        return jsonify({'success': True, 'record': updated_record})

    except ValueError:
        return jsonify({'success': False, 'message': '出场时间格式无效'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'处理出库时发生错误: {str(e)}'})

# API端点 - 获取记录详情
@app.route('/api/records/<int:record_id>', methods=['GET'])
@login_required
def get_record_details(record_id):
    parking_records = data_store.get_parking_records()
    record = next((r for r in parking_records if r['id'] == record_id), None)

    if not record:
        return jsonify({'success': False, 'message': '找不到指定的记录'})

    # 查找会员信息
    members = data_store.get_members()
    member = next((m for m in members if m['plate_number'] == record['plate_number']), None)

    # 获取折扣设置
    discount_settings = data_store.get_discount_settings()

    result = {
        'success': True,
        'record': record,
        'member': member
    }

    # 如果是会员，添加折扣信息
    if member:
        if member['membership_type'] == '普通会员':
            result['discount'] = discount_settings['normal']['discount']
        elif member['membership_type'] == 'VIP会员':
            result['discount'] = discount_settings['vip']['discount']
        elif member['membership_type'] == '企业会员':
            result['discount'] = discount_settings['corporate']['discount']

    return jsonify(result)

# 导出所有记录
@app.route('/records/export')
@login_required
def export_all_records():
    # 获取停车记录
    parking_records = data_store.get_parking_records()

    # 实现导出CSV的逻辑
    import csv
    import io
    from flask import send_file

    # 创建一个内存文件对象
    output = io.StringIO()
    writer = csv.writer(output)

    # 写入CSV头部
    writer.writerow(['ID', '车牌号', '入场时间', '出场时间', '停车时长(小时)', '费用(元)', '支付状态', '支付方式', '支付时间'])

    # 写入数据行
    for record in parking_records:
        writer.writerow([
            record.get('id', ''),
            record.get('plate_number', ''),
            record.get('entry_time').strftime('%Y-%m-%d %H:%M:%S') if record.get('entry_time') else '',
            record.get('exit_time').strftime('%Y-%m-%d %H:%M:%S') if record.get('exit_time') else '',
            record.get('duration', ''),
            record.get('fee', ''),
            '已支付' if record.get('paid', False) else '未支付',
            record.get('payment_method', ''),
            record.get('payment_time').strftime('%Y-%m-%d %H:%M:%S') if record.get('payment_time') else ''
        ])

    # 将指针移到文件开头
    output.seek(0)

    # 生成下载文件
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name='parking_records.csv'
    )

# 导出记录（带筛选）
@app.route('/export_records')
@login_required
def export_records_filtered():
    # 获取筛选参数
    plate_number = request.args.get('plate_number', '')

    # 获取停车记录
    parking_records = data_store.get_parking_records()

    # 筛选记录
    if plate_number:
        filtered_records = [r for r in parking_records if plate_number in r['plate_number']]
    else:
        filtered_records = parking_records

    # 实现导出CSV的逻辑
    import csv
    import io
    from flask import send_file

    # 创建一个内存文件对象
    output = io.StringIO()
    writer = csv.writer(output)

    # 写入CSV头部
    writer.writerow(['ID', '车牌号', '入场时间', '出场时间', '停车时长(小时)', '费用(元)', '支付状态', '支付方式', '支付时间'])

    # 写入数据行
    for record in filtered_records:
        writer.writerow([
            record.get('id', ''),
            record.get('plate_number', ''),
            record.get('entry_time').strftime('%Y-%m-%d %H:%M:%S') if record.get('entry_time') else '',
            record.get('exit_time').strftime('%Y-%m-%d %H:%M:%S') if record.get('exit_time') else '',
            record.get('duration', ''),
            record.get('fee', ''),
            '已支付' if record.get('paid', False) else '未支付',
            record.get('payment_method', ''),
            record.get('payment_time').strftime('%Y-%m-%d %H:%M:%S') if record.get('payment_time') else ''
        ])

    # 将指针移到文件开头
    output.seek(0)

    # 生成下载文件
    return send_file(
        io.BytesIO(output.getvalue().encode('utf-8-sig')),
        mimetype='text/csv',
        as_attachment=True,
        download_name='parking_records_filtered.csv'
    )

# 删除单条停车记录
@app.route('/records/delete/<int:record_id>', methods=['POST'])
@login_required
def delete_parking_record_route(record_id):
    success = data_store.delete_parking_record(record_id)
    if success:
        flash('记录删除成功！', 'success')
    else:
        flash('记录删除失败！', 'danger')
    return redirect(url_for('view_records'))

# 清空所有停车记录
@app.route('/records/clear', methods=['POST'])
@login_required
def clear_parking_records_route():
    data_store.clear_parking_records()
    flash('所有记录已清空！', 'success')
    return redirect(url_for('view_records'))

if __name__ == '__main__':
    app.run(debug=True)
