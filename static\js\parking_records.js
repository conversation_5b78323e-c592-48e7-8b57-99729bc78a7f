/**
 * 智能停车场管理系统 - 停车记录管理模块
 * 
 * 本文件包含停车记录管理页面的所有交互功能，包括：
 * 1. 查看记录详情
 * 2. 标记记录为已支付
 * 3. 显示二维码支付
 * 4. 确认二维码支付
 * 5. 手动出库
 * 6. 确认手动出库
 * 7. 删除单条记录
 * 8. 清空所有记录
 * 9. 重置搜索表单
 */

/**
 * 查看停车记录详情
 * 
 * @param {string} recordId - 停车记录ID（从HTML传入的字符串）
 */
function viewDetails(recordId) {
    // 将字符串ID转换为数字，确保API调用正确
    recordId = parseInt(recordId);
    
    // 获取详情模态框的内容区域
    const detailsModalBody = document.getElementById('detailsModalBody');
    
    // 显示加载中的提示信息
    detailsModalBody.innerHTML = `<p>正在加载记录 #${recordId} 的详细信息...</p>`;

    // 初始化并显示模态框
    const detailsModal = new bootstrap.Modal(document.getElementById('detailsModal'));
    detailsModal.show();

    // 发送AJAX请求获取记录详情
    fetch(`/api/records/${recordId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 从响应中提取数据
                const record = data.record;
                const member = data.member;
                const discount = data.discount;

                // 格式化日期显示
                const entryTime = new Date(record.entry_time);
                const exitTime = record.exit_time ? new Date(record.exit_time) : null;

                // 构建基本信息HTML
                let html = `
                    <div class="mb-3">
                        <h6>基本信息</h6>
                        <p><strong>记录ID:</strong> ${record.id}</p>
                        <p><strong>车牌号:</strong> ${record.plate_number}</p>
                        <p><strong>入场时间:</strong> ${entryTime.toLocaleString()}</p>
                `;

                // 如果已出场，显示出场相关信息
                if (exitTime) {
                    html += `
                        <p><strong>出场时间:</strong> ${exitTime.toLocaleString()}</p>
                        <p><strong>停车时长:</strong> ${record.duration} 小时</p>
                        <p><strong>费用:</strong> ${record.fee} 元</p>
                    `;

                    // 根据支付状态显示不同的标签
                    if (record.paid) {
                        html += `<p><strong>状态:</strong> <span class="badge bg-success">已支付</span></p>`;
                        // 如果有支付方式，显示支付方式
                        if (record.payment_method) {
                            html += `<p><strong>支付方式:</strong> ${record.payment_method}</p>`;
                        }
                        // 如果有支付时间，显示支付时间
                        if (record.payment_time) {
                            const paymentTime = new Date(record.payment_time);
                            html += `<p><strong>支付时间:</strong> ${paymentTime.toLocaleString()}</p>`;
                        }
                    } else {
                        html += `<p><strong>状态:</strong> <span class="badge bg-warning">未支付</span></p>`;
                    }
                } else {
                    // 如果未出场，显示停车中状态
                    html += `<p><strong>状态:</strong> <span class="badge bg-info">停车中</span></p>`;
                }

                html += `</div>`;

                // 显示会员信息（如果有）
                if (member) {
                    html += `
                        <div>
                            <h6>会员信息</h6>
                            <p><strong>会员状态:</strong> <span class="badge bg-success">是</span></p>
                            <p><strong>会员姓名:</strong> ${member.name}</p>
                            <p><strong>会员类型:</strong> ${member.membership_type}</p>
                    `;

                    // 如果有折扣，显示折扣率
                    if (discount) {
                        html += `<p><strong>折扣率:</strong> ${discount}%</p>`;
                    }

                    html += `</div>`;
                } else {
                    // 如果不是会员，显示非会员状态
                    html += `
                        <div>
                            <h6>会员信息</h6>
                            <p><strong>会员状态:</strong> <span class="badge bg-secondary">否</span></p>
                        </div>
                    `;
                }

                // 更新模态框内容
                detailsModalBody.innerHTML = html;
            } else {
                // 显示错误信息
                detailsModalBody.innerHTML = `<div class="alert alert-danger">${data.message || '加载记录详情失败'}</div>`;
            }
        })
        .catch(error => {
            // 处理网络错误或其他异常
            console.error('Error:', error);
            detailsModalBody.innerHTML = `<div class="alert alert-danger">加载记录详情时发生错误</div>`;
        });
}

/**
 * 标记停车记录为已支付
 * 
 * @param {string} recordId - 停车记录ID（从HTML传入的字符串）
 */
function markAsPaid(recordId) {
    // 将字符串ID转换为数字，确保API调用正确
    recordId = parseInt(recordId);
    
    // 设置隐藏字段的值
    document.getElementById('record_id').value = recordId;

    // 获取记录详情以填充支付金额
    fetch(`/api/records/${recordId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.record && data.record.fee) {
                // 设置默认支付金额
                document.getElementById('payment_amount').value = data.record.fee;

                // 如果是会员且有折扣，计算折扣后金额
                if (data.member && data.discount) {
                    // 计算折扣后金额并保留两位小数
                    const discountedAmount = (data.record.fee * data.discount / 100).toFixed(2);
                    document.getElementById('payment_amount').value = discountedAmount;
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });

    // 显示支付模态框
    const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
    paymentModal.show();
}

/**
 * 提交支付信息
 * 处理用户在支付模态框中输入的支付信息
 */
function submitPayment() {
    const form = document.getElementById('paymentForm');
    
    // 验证表单是否有效
    if (form.checkValidity()) {
        // 获取表单数据
        const recordId = document.getElementById('record_id').value;
        const paymentMethod = document.getElementById('payment_method').value;
        const paymentAmount = document.getElementById('payment_amount').value;

        // 创建表单数据对象
        const formData = new FormData();
        formData.append('record_id', recordId);
        formData.append('payment_method', paymentMethod);
        formData.append('payment_amount', paymentAmount);

        // 发送AJAX请求处理支付
        fetch('/api/payment/mark_paid', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 关闭模态框
                const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
                paymentModal.hide();

                // 显示成功消息
                alert('支付成功！');

                // 刷新页面以显示更新后的数据
                location.reload();
            } else {
                // 显示错误消息
                alert(data.message || '支付失败，请重试');
            }
        })
        .catch(error => {
            // 处理网络错误或其他异常
            console.error('Error:', error);
            alert('支付处理时发生错误，请重试');
        });
    } else {
        // 如果表单无效，显示验证消息
        form.reportValidity();
    }
}

/**
 * 重置搜索表单
 * 清除所有搜索条件并返回到未筛选的记录列表
 */
function resetForm() {
    // 重置表单字段
    document.getElementById('searchForm').reset();
    
    // 重定向到未带参数的记录页面
    window.location.href = recordsUrl; // 这个变量需要在HTML中定义
}

/**
 * 显示二维码支付界面
 * 
 * @param {string} recordId - 停车记录ID（从HTML传入的字符串）
 * @param {string} fee - 停车费用（从HTML传入的字符串）
 */
function showQRCodePayment(recordId, fee) {
    // 将字符串ID和费用转换为数字，确保API调用正确
    recordId = parseInt(recordId);
    fee = parseFloat(fee);
    
    // 设置隐藏字段的值
    document.getElementById('qrcode_record_id').value = recordId;

    // 获取记录详情以填充支付金额
    fetch(`/api/records/${recordId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.record && data.record.fee) {
                let amount = data.record.fee;

                // 如果是会员且有折扣，计算折扣后金额
                if (data.member && data.discount) {
                    amount = (data.record.fee * data.discount / 100).toFixed(2);
                }

                // 设置金额显示
                document.getElementById('qrcode_amount').textContent = `¥${amount}`;

                // 设置二维码图片URL
                // 注意：这里使用的是示例图片，实际应用中应该生成真实的支付二维码
                document.getElementById('wechatQRCode').src = `/static/img/your_wechat_qrcode.jpg`;
                document.getElementById('alipayQRCode').src = `/static/img/your_alipay_qrcode.jpg`;
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });

    // 显示二维码支付模态框
    const qrcodeModal = new bootstrap.Modal(document.getElementById('qrcodePaymentModal'));
    qrcodeModal.show();
}

/**
 * 确认二维码支付完成
 * 用户扫码支付后，点击"已完成支付"按钮触发此函数
 */
function confirmQRCodePayment() {
    // 获取记录ID和支付金额
    const recordId = document.getElementById('qrcode_record_id').value;
    const amount = document.getElementById('qrcode_amount').textContent.replace('¥', '');

    // 创建表单数据
    const formData = new FormData();
    formData.append('record_id', recordId);
    formData.append('payment_method', '扫码支付');
    formData.append('payment_amount', amount);

    // 发送AJAX请求处理支付
    fetch('/api/payment/mark_paid', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 关闭模态框
            const qrcodeModal = bootstrap.Modal.getInstance(document.getElementById('qrcodePaymentModal'));
            qrcodeModal.hide();

            // 显示成功消息
            alert('支付成功！');

            // 刷新页面以显示更新后的数据
            location.reload();
        } else {
            // 显示错误消息
            alert(data.message || '支付失败，请重试');
        }
    })
    .catch(error => {
        // 处理网络错误或其他异常
        console.error('Error:', error);
        alert('支付处理时发生错误，请重试');
    });
}

/**
 * 手动出库
 * 用于车辆未正常通过出口闸机时的手动处理
 * 
 * @param {string} recordId - 停车记录ID（从HTML传入的字符串）
 */
function manualExit(recordId) {
    // 将字符串ID转换为数字，确保API调用正确
    recordId = parseInt(recordId);
    
    // 设置隐藏字段的值
    document.getElementById('exit_record_id').value = recordId;

    // 设置默认出场时间为当前时间
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要+1
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    // 设置datetime-local输入框的值，格式为：YYYY-MM-DDThh:mm
    document.getElementById('exit_time').value = `${year}-${month}-${day}T${hours}:${minutes}`;

    // 显示手动出库模态框
    const manualExitModal = new bootstrap.Modal(document.getElementById('manualExitModal'));
    manualExitModal.show();
}

/**
 * 确认手动出库
 * 处理用户在手动出库模态框中输入的信息
 */
function confirmManualExit() {
    const form = document.getElementById('manualExitForm');
    
    // 验证表单是否有效
    if (form.checkValidity()) {
        // 获取表单数据
        const recordId = document.getElementById('exit_record_id').value;
        const exitTime = document.getElementById('exit_time').value;
        const exitNote = document.getElementById('exit_note').value;

        // 创建表单数据
        const formData = new FormData();
        formData.append('record_id', recordId);
        formData.append('exit_time', exitTime);
        formData.append('exit_note', exitNote);

        // 发送AJAX请求处理手动出库
        fetch('/api/records/manual_exit', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 关闭模态框
                const manualExitModal = bootstrap.Modal.getInstance(document.getElementById('manualExitModal'));
                manualExitModal.hide();

                // 显示成功消息
                alert('手动出库成功！');

                // 刷新页面以显示更新后的数据
                location.reload();
            } else {
                // 显示错误消息
                alert(data.message || '手动出库失败，请重试');
            }
        })
        .catch(error => {
            // 处理网络错误或其他异常
            console.error('Error:', error);
            alert('手动出库处理时发生错误，请重试');
        });
    } else {
        // 如果表单无效，显示验证消息
        form.reportValidity();
    }
}

/**
 * 确认删除单条停车记录
 * 
 * @param {string} recordId - 停车记录ID（从HTML传入的字符串）
 */
function confirmDeleteRecord(recordId) {
    // 将字符串ID转换为数字，确保API调用正确
    recordId = parseInt(recordId);
    
    // 显示确认对话框
    if (confirm('确定要删除ID为 ' + recordId + ' 的停车记录吗？此操作不可恢复！')) {
        // 创建表单并提交
        const form = document.createElement('form');
        form.method = 'POST';
        // 使用Flask的url_for生成URL，并替换占位符
        form.action = deleteRecordUrl.replace('0', recordId); // 这个变量需要在HTML中定义
        document.body.appendChild(form);
        form.submit();
    }
}

/**
 * 确认清空所有停车记录
 * 先导出所有记录，然后清空数据库中的记录
 */
function confirmClearRecords() {
    // 显示确认对话框
    if (confirm('确定要导出并清空所有停车记录吗？此操作将先导出所有记录，然后清空页面显示的记录。')) {
        // 先导出记录
        window.location.href = exportRecordsUrl; // 这个变量需要在HTML中定义

        // 延迟2秒后提示用户记录已导出并提交清空表单
        // 注意：这种方式依赖于浏览器下载文件的行为，可能不是最可靠的方法
        setTimeout(function() {
            alert('停车记录已成功导出！请在导出的文件中查看历史记录。');
            // 提交清空记录的表单
            document.getElementById('clearRecordsForm').submit();
        }, 2000);
    }
}
