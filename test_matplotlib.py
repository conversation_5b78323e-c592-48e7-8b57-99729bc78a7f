#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试matplotlib配置的脚本
"""

import matplotlib
print(f"当前matplotlib后端: {matplotlib.get_backend()}")

# 设置非交互式后端
matplotlib.use('Agg')
print(f"设置后的matplotlib后端: {matplotlib.get_backend()}")

import matplotlib.pyplot as plt
# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
import io
import base64

def test_chart_generation():
    """测试图表生成功能"""
    try:
        # 创建测试数据
        x = [1, 2, 3, 4, 5]
        y = [10, 20, 15, 25, 30]

        # 创建图表
        plt.figure(figsize=(8, 6))
        plt.plot(x, y, marker='o')
        plt.title('测试图表')
        plt.xlabel('X轴')
        plt.ylabel('Y轴')
        plt.grid(True)

        # 保存为base64
        buffer = io.BytesIO()
        plt.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
        buffer.seek(0)
        chart_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

        # 清理资源
        plt.close()
        buffer.close()

        print("✓ 图表生成成功")
        print(f"Base64数据长度: {len(chart_data)}")
        return True

    except Exception as e:
        print(f"✗ 图表生成失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == '__main__':
    print("开始测试matplotlib配置...")
    success = test_chart_generation()
    if success:
        print("matplotlib配置正常，可以在Web应用中使用")
    else:
        print("matplotlib配置有问题，需要检查")
