<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tesseract OCR 安装指南 - 智能停车场管理系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Tesseract OCR 安装指南</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            车牌识别功能需要安装 Tesseract OCR 才能正常工作。请按照以下步骤安装。
                        </div>

                        <h5 class="mt-4">Windows 安装步骤</h5>
                        <ol class="mt-3">
                            <li class="mb-3">
                                <strong>下载 Tesseract OCR 安装程序：</strong>
                                <ul>
                                    <li>访问 <a href="https://github.com/UB-Mannheim/tesseract/wiki" target="_blank">https://github.com/UB-Mannheim/tesseract/wiki</a></li>
                                    <li>下载最新版本的安装程序（例如：tesseract-ocr-w64-setup-v5.3.3.20231005.exe）</li>
                                </ul>
                            </li>
                            <li class="mb-3">
                                <strong>运行安装程序：</strong>
                                <ul>
                                    <li>选择"Additional language data (download)"选项</li>
                                    <li>在语言选择中，确保选择了"Chinese (Simplified)"和"English"</li>
                                    <li>记住安装路径（默认为 <code>C:\Program Files\Tesseract-OCR</code>）</li>
                                </ul>
                            </li>
                            <li class="mb-3">
                                <strong>添加环境变量：</strong>
                                <ul>
                                    <li>右键点击"此电脑"，选择"属性"</li>
                                    <li>点击"高级系统设置"</li>
                                    <li>点击"环境变量"</li>
                                    <li>在"系统变量"部分，找到"Path"变量并编辑</li>
                                    <li>添加 Tesseract 安装路径（例如：<code>C:\Program Files\Tesseract-OCR</code>）</li>
                                    <li>点击"确定"保存更改</li>
                                </ul>
                            </li>
                            <li class="mb-3">
                                <strong>验证安装：</strong>
                                <ul>
                                    <li>重新打开命令提示符或 PowerShell</li>
                                    <li>运行 <code>tesseract --version</code> 命令</li>
                                    <li>如果显示版本信息，则安装成功</li>
                                </ul>
                            </li>
                        </ol>

                        <div class="alert alert-warning mt-4">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>重要提示：</strong> 安装完成后，请重启应用程序以使更改生效。
                        </div>

                        <div class="text-center mt-4">
                            <a href="/recognize/upload" class="btn btn-primary">
                                <i class="bi bi-arrow-left me-2"></i>返回上传识别页面
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
