# Tesseract OCR 安装指南

## Windows 安装步骤

1. 下载 Tesseract OCR 安装程序：
   - 访问 https://github.com/UB-Mannheim/tesseract/wiki
   - 下载最新版本的安装程序（例如：tesseract-ocr-w64-setup-v5.3.3.20231005.exe）

2. 运行安装程序：
   - 选择"Additional language data (download)"选项
   - 在语言选择中，确保选择了"Chinese (Simplified)"和"English"
   - 记住安装路径（默认为 `C:\Program Files\Tesseract-OCR`）

3. 添加环境变量：
   - 右键点击"此电脑"，选择"属性"
   - 点击"高级系统设置"
   - 点击"环境变量"
   - 在"系统变量"部分，找到"Path"变量并编辑
   - 添加 Tesseract 安装路径（例如：`C:\Program Files\Tesseract-OCR`）
   - 点击"确定"保存更改

4. 验证安装：
   - 重新打开命令提示符或 PowerShell
   - 运行 `tesseract --version` 命令
   - 如果显示版本信息，则安装成功

## 安装后配置

安装完成后，需要确保 Python 代码中的 Tesseract 路径设置正确：

```python
# 在 recognition/plate_recognizer.py 文件中添加以下代码
import pytesseract

# 设置 Tesseract 路径（根据实际安装路径调整）
pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
```

安装完成后，重启应用程序以使更改生效。
