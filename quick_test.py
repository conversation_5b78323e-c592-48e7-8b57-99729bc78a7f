#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快速测试车牌识别功能
"""

import os
import sys

def test_plate_recognition(image_path):
    """快速测试车牌识别"""
    print(f"测试图像: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"错误: 图像文件不存在 - {image_path}")
        return
    
    try:
        # 导入车牌识别模块
        from recognition.plate_recognizer import recognize_plate_from_image
        
        print("开始识别...")
        
        # 尝试识别（跳过车辆检测）
        plate_number = recognize_plate_from_image(image_path, skip_vehicle_detection=True)
        
        if plate_number:
            print(f"✓ 识别成功: {plate_number}")
        else:
            print("✗ 识别失败")
            
            # 尝试诊断
            print("\n运行诊断...")
            try:
                from diagnosis_tool import test_image_loading, test_plate_recognition_direct
                
                # 测试图像加载
                if test_image_loading(image_path):
                    # 测试直接识别
                    result = test_plate_recognition_direct(image_path)
                    if result:
                        print(f"诊断识别成功: {result}")
                    else:
                        print("诊断也无法识别")
                        
            except Exception as e:
                print(f"诊断出错: {str(e)}")
        
    except Exception as e:
        print(f"测试出错: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def main():
    if len(sys.argv) < 2:
        print("用法: python quick_test.py <图像路径>")
        print("例如: python quick_test.py uploads/test_plate.jpg")
        
        # 查找uploads目录中的图像文件
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            image_files = [f for f in os.listdir(uploads_dir) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
            if image_files:
                print(f"\n在 {uploads_dir} 目录中找到以下图像文件:")
                for i, file in enumerate(image_files, 1):
                    print(f"{i}. {file}")
                print(f"\n可以使用: python quick_test.py {uploads_dir}/{image_files[0]}")
        return
    
    image_path = sys.argv[1]
    test_plate_recognition(image_path)

if __name__ == "__main__":
    main()
