#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
二维码上传路由模块
"""

import os
from flask import Blueprint, request, jsonify, current_app, flash, redirect, url_for, render_template
from werkzeug.utils import secure_filename
from upload_qrcode import upload_payment_qrcode

# 创建蓝图
qrcode_bp = Blueprint('qrcode', __name__)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@qrcode_bp.route('/upload_qrcode', methods=['GET', 'POST'])
def upload_qrcode():
    """上传支付二维码页面和处理"""
    if request.method == 'POST':
        # 检查是否有文件部分
        if 'wechat_qrcode' not in request.files and 'alipay_qrcode' not in request.files:
            flash('没有选择文件', 'danger')
            return redirect(request.url)
        
        # 处理微信二维码上传
        if 'wechat_qrcode' in request.files:
            file = request.files['wechat_qrcode']
            if file.filename != '' and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
                file.save(temp_path)
                try:
                    upload_payment_qrcode(temp_path, 'wechat')
                    flash('微信支付二维码上传成功', 'success')
                except Exception as e:
                    flash(f'微信支付二维码处理失败: {str(e)}', 'danger')
        
        # 处理支付宝二维码上传
        if 'alipay_qrcode' in request.files:
            file = request.files['alipay_qrcode']
            if file.filename != '' and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
                file.save(temp_path)
                try:
                    upload_payment_qrcode(temp_path, 'alipay')
                    flash('支付宝支付二维码上传成功', 'success')
                except Exception as e:
                    flash(f'支付宝支付二维码处理失败: {str(e)}', 'danger')
        
        return redirect(url_for('qrcode.upload_qrcode'))
    
    return render_template('upload_qrcode.html')

# 注册蓝图的函数
def register_qrcode_routes(app):
    app.register_blueprint(qrcode_bp, url_prefix='/qrcode')