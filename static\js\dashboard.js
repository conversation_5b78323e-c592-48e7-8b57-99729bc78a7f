// 仪表板脚本
document.addEventListener('DOMContentLoaded', function() {
    // 自动关闭警告消息
    const alerts = document.querySelectorAll('.alert-dismissible');
    alerts.forEach(alert => {
        setTimeout(() => {
            const closeButton = alert.querySelector('.btn-close');
            if (closeButton) {
                closeButton.click();
            }
        }, 5000);
    });

    // 显示当前日期
    updateCurrentDate();

    // 初始化图表（如果存在）
    if (typeof Chart !== 'undefined') {
        initCharts();
    }

    // 初始化表格排序（如果存在）
    if (typeof DataTable !== 'undefined') {
        initDataTables();
    }
});

// 更新当前日期显示
function updateCurrentDate() {
    const currentDateElement = document.getElementById('current-date');
    if (currentDateElement) {
        const now = new Date();
        const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
        currentDateElement.textContent = now.toLocaleDateString('zh-CN', options);
    }
}

// 初始化图表
function initCharts() {
    // 收入趋势图
    const incomeChartEl = document.getElementById('incomeChart');
    if (incomeChartEl) {
        const ctx = incomeChartEl.getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: incomeData.labels,
                datasets: [{
                    label: '收入（元）',
                    data: incomeData.values,
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    pointRadius: 3,
                    pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointBorderColor: 'rgba(78, 115, 223, 1)',
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    // 车流量图
    const trafficChartEl = document.getElementById('trafficChart');
    if (trafficChartEl) {
        const ctx = trafficChartEl.getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: trafficData.labels,
                datasets: [{
                    label: '车辆数量',
                    data: trafficData.values,
                    backgroundColor: 'rgba(28, 200, 138, 0.8)',
                    borderColor: 'rgba(28, 200, 138, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
}

// 初始化数据表格
function initDataTables() {
    const tables = document.querySelectorAll('.datatable');
    tables.forEach(table => {
        new DataTable(table, {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Chinese.json'
            },
            pageLength: 10,
            lengthMenu: [5, 10, 25, 50],
            responsive: true
        });
    });
}
