/**
 * 报表页面的JavaScript功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 设置开始日期为今天的按钮
    const setTodayStartBtn = document.getElementById('set-today-start');
    if (setTodayStartBtn) {
        setTodayStartBtn.addEventListener('click', function() {
            const today = new Date();
            const formattedDate = formatDate(today);
            document.getElementById('start_date').value = formattedDate;
        });
    }

    // 设置结束日期为今天的按钮
    const setTodayEndBtn = document.getElementById('set-today-end');
    if (setTodayEndBtn) {
        setTodayEndBtn.addEventListener('click', function() {
            const today = new Date();
            const formattedDate = formatDate(today);
            document.getElementById('end_date').value = formattedDate;
        });
    }

    // 根据报表类型自动设置日期范围
    const reportTypeSelect = document.getElementById('report_type');
    if (reportTypeSelect) {
        reportTypeSelect.addEventListener('change', function() {
            setDateRangeByReportType(this.value);
        });
    }
});

/**
 * 格式化日期为YYYY-MM-DD格式
 * 
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

/**
 * 根据报表类型设置合适的日期范围
 * 
 * @param {string} reportType - 报表类型
 */
function setDateRangeByReportType(reportType) {
    const today = new Date();
    let startDate = new Date();
    
    // 根据报表类型设置开始日期
    switch (reportType) {
        case 'daily':
            // 当天
            break;
        case 'weekly':
            // 一周前
            startDate.setDate(today.getDate() - 7);
            break;
        case 'monthly':
            // 一个月前
            startDate.setMonth(today.getMonth() - 1);
            break;
        case 'yearly':
            // 一年前
            startDate.setFullYear(today.getFullYear() - 1);
            break;
    }
    
    // 设置日期输入框的值
    document.getElementById('start_date').value = formatDate(startDate);
    document.getElementById('end_date').value = formatDate(today);
}
