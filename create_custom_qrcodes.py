#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建自定义二维码图片
"""

import os
import qrcode
from PIL import Image, ImageDraw, ImageFont

def create_custom_qrcode(payment_type, filename, data):
    """创建自定义二维码图片"""
    # 创建目录
    os.makedirs('static/img', exist_ok=True)
    
    # 设置颜色和标题
    if payment_type == 'wechat':
        color = (0, 157, 78)  # 微信绿色
        title = "微信支付"
    elif payment_type == 'alipay':
        color = (0, 103, 198)  # 支付宝蓝色
        title = "支付宝支付"
    else:
        color = (0, 0, 0)  # 黑色
        title = "扫码支付"
    
    # 生成二维码
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,
        box_size=10,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)
    
    # 创建二维码图片
    img = qr.make_image(fill_color="black", back_color="white").convert('RGB')
    
    # 添加标题
    # 创建一个新的图片，比二维码大一些，以便添加标题
    width, height = img.size
    new_img = Image.new('RGB', (width, height + 40), (255, 255, 255))
    new_img.paste(img, (0, 40))
    
    # 添加标题
    draw = ImageDraw.Draw(new_img)
    try:
        # 尝试加载字体，如果失败则使用默认字体
        font = ImageFont.truetype("arial.ttf", 24)
    except IOError:
        font = ImageFont.load_default()
    
    # 计算文本位置，使其居中
    text_width = draw.textlength(title, font=font)
    position = ((width - text_width) / 2, 10)
    
    # 绘制标题
    draw.text(position, title, font=font, fill=color)
    
    # 保存图片
    full_path = os.path.join('static/img', filename)
    new_img.save(full_path)
    print(f"二维码已保存到 {full_path}")
    
    return full_path

if __name__ == "__main__":
    # 创建微信支付二维码
    create_custom_qrcode(
        'wechat', 
        'your_wechat_qrcode.jpg', 
        "weixin://wxpay/bizpayurl?pr=您的微信收款码"
    )
    
    # 创建支付宝支付二维码
    create_custom_qrcode(
        'alipay', 
        'your_alipay_qrcode.jpg', 
        "https://qr.alipay.com/您的支付宝收款码"
    )
