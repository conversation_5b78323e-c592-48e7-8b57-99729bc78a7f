#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
车牌识别诊断工具
用于排查车牌识别失败的原因
"""

import os
import sys
import cv2
import shutil
import traceback
from pathlib import Path

def check_dependencies():
    """检查依赖项是否正确安装"""
    print("=== 依赖项检查 ===")
    
    # 检查 OpenCV
    try:
        import cv2
        print(f"✓ OpenCV 版本: {cv2.__version__}")
    except ImportError:
        print("✗ OpenCV 未安装")
        return False
    
    # 检查 Tesseract
    tesseract_installed = shutil.which('tesseract') is not None
    if tesseract_installed:
        try:
            import pytesseract
            version = pytesseract.get_tesseract_version()
            print(f"✓ Tesseract OCR 版本: {version}")
        except Exception as e:
            print(f"✗ Tesseract OCR 配置错误: {str(e)}")
            return False
    else:
        print("✗ Tesseract OCR 未安装")
        return False
    
    # 检查 YOLO 模型
    try:
        from ultralytics import YOLO
        print("✓ YOLOv8 库已安装")
        
        # 检查模型文件
        model_path = "yolov8n.pt"
        if os.path.exists(model_path):
            print(f"✓ YOLO 模型文件存在: {model_path}")
        else:
            print(f"! YOLO 模型文件不存在: {model_path}，将尝试下载")
    except ImportError:
        print("✗ YOLOv8 库未安装")
        return False
    
    return True

def test_image_loading(image_path):
    """测试图像加载"""
    print(f"\n=== 图像加载测试: {image_path} ===")
    
    if not os.path.exists(image_path):
        print(f"✗ 图像文件不存在: {image_path}")
        return False
    
    try:
        img = cv2.imread(image_path)
        if img is None:
            print(f"✗ 无法读取图像: {image_path}")
            return False
        
        height, width = img.shape[:2]
        print(f"✓ 图像尺寸: {width}x{height}")
        print(f"✓ 图像通道数: {img.shape[2] if len(img.shape) == 3 else 1}")
        
        # 检查图像质量
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        print(f"✓ 图像清晰度: {laplacian_var:.2f} {'(清晰)' if laplacian_var > 100 else '(模糊)'}")
        
        return True
    except Exception as e:
        print(f"✗ 图像加载错误: {str(e)}")
        return False

def test_vehicle_detection(image_path):
    """测试车辆检测"""
    print(f"\n=== 车辆检测测试: {image_path} ===")
    
    try:
        from recognition.vehicle_detector import debug_detection
        
        # 进行车辆检测调试
        vehicle_detected, vis_img = debug_detection(image_path, save_visualization=True)
        
        if vehicle_detected:
            print("✓ 检测到车辆")
        else:
            print("✗ 未检测到车辆")
            print("  可能原因:")
            print("  1. 图像中没有车辆")
            print("  2. 车辆被遮挡或不完整")
            print("  3. 图像质量不佳")
            print("  4. YOLO模型未正确加载")
        
        # 保存可视化结果
        vis_path = image_path.replace('.jpg', '_detection.jpg').replace('.png', '_detection.png')
        if os.path.exists(vis_path):
            print(f"✓ 检测可视化结果已保存: {vis_path}")
        
        return vehicle_detected
        
    except Exception as e:
        print(f"✗ 车辆检测错误: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_plate_recognition_direct(image_path):
    """直接测试车牌识别（跳过车辆检测）"""
    print(f"\n=== 直接车牌识别测试: {image_path} ===")
    
    try:
        from recognition.plate_recognizer import preprocess_image, detect_plate_regions, recognize_text_from_plate, validate_plate_number
        
        # 1. 图像预处理
        print("步骤1: 图像预处理...")
        processed_img, original_img = preprocess_image(image_path)
        if processed_img is None or original_img is None:
            print("✗ 图像预处理失败")
            return None
        print("✓ 图像预处理成功")
        
        # 2. 检测车牌区域
        print("步骤2: 检测车牌区域...")
        plate_regions = detect_plate_regions(processed_img, original_img)
        if not plate_regions:
            print("✗ 未检测到车牌区域")
            return None
        print(f"✓ 检测到 {len(plate_regions)} 个可能的车牌区域")
        
        # 3. 对每个区域进行文本识别
        print("步骤3: 文本识别...")
        for i, plate_img in enumerate(plate_regions):
            print(f"  识别区域 {i+1}...")
            plate_number = recognize_text_from_plate(plate_img)
            
            if plate_number:
                print(f"  原始识别结果: {plate_number}")
                
                # 验证车牌号格式
                valid_plate = validate_plate_number(plate_number)
                if valid_plate:
                    print(f"✓ 识别成功: {valid_plate}")
                    return valid_plate
                else:
                    print(f"  格式验证失败: {plate_number}")
            else:
                print(f"  区域 {i+1} 未识别到文本")
        
        print("✗ 所有区域都未能识别出有效车牌")
        return None
        
    except Exception as e:
        print(f"✗ 车牌识别错误: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        return None

def test_full_recognition(image_path):
    """测试完整的车牌识别流程"""
    print(f"\n=== 完整识别流程测试: {image_path} ===")
    
    try:
        from recognition.plate_recognizer import recognize_plate_from_image
        
        plate_number = recognize_plate_from_image(image_path)
        
        if plate_number:
            print(f"✓ 完整流程识别成功: {plate_number}")
        else:
            print("✗ 完整流程识别失败")
        
        return plate_number
        
    except Exception as e:
        print(f"✗ 完整流程错误: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        return None

def main():
    """主函数"""
    print("车牌识别诊断工具")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("用法: python diagnosis_tool.py <图像路径>")
        print("例如: python diagnosis_tool.py test_image.jpg")
        return
    
    image_path = sys.argv[1]
    
    # 1. 检查依赖项
    if not check_dependencies():
        print("\n请先安装缺失的依赖项")
        return
    
    # 2. 测试图像加载
    if not test_image_loading(image_path):
        print("\n图像加载失败，请检查图像文件")
        return
    
    # 3. 测试车辆检测
    vehicle_detected = test_vehicle_detection(image_path)
    
    # 4. 直接测试车牌识别
    direct_result = test_plate_recognition_direct(image_path)
    
    # 5. 测试完整流程
    full_result = test_full_recognition(image_path)
    
    # 总结
    print("\n" + "=" * 50)
    print("诊断总结:")
    print(f"车辆检测: {'通过' if vehicle_detected else '失败'}")
    print(f"直接车牌识别: {'成功 - ' + direct_result if direct_result else '失败'}")
    print(f"完整流程识别: {'成功 - ' + full_result if full_result else '失败'}")
    
    if not full_result:
        print("\n建议:")
        if not vehicle_detected:
            print("1. 车辆检测失败，可能需要:")
            print("   - 确保图像中包含完整的车辆")
            print("   - 提高图像质量和清晰度")
            print("   - 检查YOLO模型是否正确加载")
        if not direct_result:
            print("2. 车牌识别失败，可能需要:")
            print("   - 确保车牌在图像中清晰可见")
            print("   - 检查Tesseract OCR配置")
            print("   - 尝试不同的图像预处理参数")

if __name__ == "__main__":
    main()
